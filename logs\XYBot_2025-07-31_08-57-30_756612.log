2025-07-31 08:57:31 | SUCCESS | 读取主设置成功
2025-07-31 08:57:31 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 GetRedisAddr: 127.0.0.1:6379
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 Server start at :9000
2025-07-31 08:57:32 | SUCCESS | WechatAPI服务已启动
2025-07-31 08:57:33 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-31 08:57:33 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-31 08:57:33 | SUCCESS | 登录成功
2025-07-31 08:57:33 | SUCCESS | 已开启自动心跳
2025-07-31 08:57:33 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 08:57:33 | SUCCESS | 数据库初始化成功
2025-07-31 08:57:33 | SUCCESS | 定时任务已启动
2025-07-31 08:57:33 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-31 08:57:33 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:34 | INFO | 播客API初始化成功
2025-07-31 08:57:34 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 08:57:34 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-31 08:57:34 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-31 08:57:34 | INFO | [ChatSummary] 数据库初始化成功
2025-07-31 08:57:34 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-31 08:57:34 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-31 08:57:34 | DEBUG |   - 启用状态: True
2025-07-31 08:57:34 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-31 08:57:34 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-31 08:57:34 | DEBUG |   - Web ID: 7468716986638386703
2025-07-31 08:57:34 | DEBUG |   - Cookies配置: 已配置
2025-07-31 08:57:34 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-31 08:57:34 | DEBUG |   - 自然化响应: True
2025-07-31 08:57:34 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-31 08:57:34 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-31 08:57:34 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-31 08:57:34 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-31 08:57:34 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-31 08:57:34 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-31 08:57:34 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-31 08:57:34 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:34 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-31 08:57:34 | INFO | [RenameReminder] 开始启用插件...
2025-07-31 08:57:34 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-31 08:57:34 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-31 08:57:34 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-31 08:57:34 | INFO | 已设置检查间隔为 3600 秒
2025-07-31 08:57:34 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-31 08:57:35 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-31 08:57:35 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-31 08:57:35 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-31 08:57:36 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-31 08:57:36 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-31 08:57:36 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:36 | INFO | [yuanbao] 插件初始化完成
2025-07-31 08:57:36 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-31 08:57:36 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-31 08:57:36 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-31 08:57:36 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-31 08:57:36 | INFO | 处理堆积消息中
2025-07-31 08:57:36 | SUCCESS | 处理堆积消息完毕
2025-07-31 08:57:36 | SUCCESS | 开始处理消息
2025-07-31 08:58:17 | DEBUG | 收到消息: {'MsgId': 334963301, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923499, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_GEuAWdzW|v1_BbgV9Kos</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 6230923667577072468, 'MsgSeq': 871413262}
2025-07-31 08:58:17 | INFO | 收到文本消息: 消息ID:334963301 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753923497.89秒前, 等待时间: 0.00秒
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女'
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7468716989062841895, Web ID: 7468716986638386703
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 1983
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 生成会话ID: 38175392349789148, 消息ID: ba0d6520-966a-11f0-a188-0d4d3cc8de1f
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求头: {'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf', 'Host': 'www.doubao.com', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'x-flow-trace': '04-ea34af681cc347f1-0cb6bf4bd6964895-01', 'last-event-id': 'undefined', 'Agw-Js-Conv': 'str, str', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty'}
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": false, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "section_id": "381753923449789148", "conversation_id": "38175392349789148", "local_message_id": "ba0d6520-966a-11f0-a188-0d4d3cc8de1f"}
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7468716989062841895&device_plat...
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 606 字符
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Thu, 31 Jul 2025 00:58:20 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '202507310858203FE86F81471A8E1F1CB1', 'server-timing': 'inner; dur=216,tt_agw; dur=206', 'x-ms-token': 'Y6ecNjcwOqu4VKY30I2VFMV2805kvcZcXwOMkO9_5TabrgdFkHQbhHE8Aclwo4-2gG6X0rFiVKDv5SmT07Mrb6KAo-v6IDmv3aTWPYVn', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f8491c78595c821a0a46d144c25c6865986f1e5ce3dc0c1d3b8fe0826c8a498766e7b65144876755657ec26e18a8d2c94eacdf5ab47f65d31a400f63672618f95dc3b46d538a0ae2b0f5e1ac16bf0c206c07541c36155f26d684cd6243ee03ca9d3', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-2507310858203FE86F81471A8E1F1CB1-526FB9096016C4CC-00', 'x-tt-timestamp': '1753923500.749', 'via': 'cache26.cn7023[264,0]', 'timing-allow-origin': '*', 'eagleid': 'db999b2e17539235005061797e'}
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 252 字节
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 252 字节
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 244 字符
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 176
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 事件类型: 2005
2025-07-31 08:58:18 | WARNING | [DoubaoVideoSearch] 收到错误事件: {'code': 710020702, 'message': 'system error', 'error_detail': {'code': 710020702, 'locale': 'zh', 'message': '系统错误'}}
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 52
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 事件类型: 2003
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 收到结束事件
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 结束事件：返回文本结果
2025-07-31 08:58:18 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: text
2025-07-31 08:58:18 | WARNING | [DoubaoVideoSearch] 结果类型不是视频: text
2025-07-31 08:58:18 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:这个不行
2025-07-31 08:58:18 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.95秒
2025-07-31 08:58:18 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 08:58:18 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-31 08:58:18 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-31 08:58:44 | DEBUG | 收到消息: {'MsgId': 2022627883, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="032cd0b4629d09fb554cebc1dde6723b" encryver="1" cdnthumbaeskey="032cd0b4629d09fb554cebc1dde6723b" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" cdnthumblength="3694" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" length="616701" md5="760f204087535aa6dea45a2e9d489e79" hevc_mid_size="46832" originsourcemd5="b7caba657a51fb2014a8f1b176f01bc5">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwNzAwMDIwMTAxMDEyYzAiLCJwZHFIYXNoIjoiZWQ0ZjIwODE3MDNlMDIzZWJm\nZWRjMDg3NDBkZTY0NmU4ZmY1YjkxMDAxOWIzZmU0ZTY2NDlmZTA4MDFhNjZmZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923526, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>ab958609c1b0886c30730195f26ecc3e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MFYrL1ks|v1_/Q2j5NKD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 2051451242008084923, 'MsgSeq': 871413265}
2025-07-31 08:58:44 | INFO | 收到图片消息: 消息ID:2022627883 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="032cd0b4629d09fb554cebc1dde6723b" encryver="1" cdnthumbaeskey="032cd0b4629d09fb554cebc1dde6723b" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" cdnthumblength="3694" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" length="616701" md5="760f204087535aa6dea45a2e9d489e79" hevc_mid_size="46832" originsourcemd5="b7caba657a51fb2014a8f1b176f01bc5"><secHashInfoBase64>eyJwaGFzaCI6IjEwNzAwMDIwMTAxMDEyYzAiLCJwZHFIYXNoIjoiZWQ0ZjIwODE3MDNlMDIzZWJmZWRjMDg3NDBkZTY0NmU4ZmY1YjkxMDAxOWIzZmU0ZTY2NDlmZTA4MDFhNjZmZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 08:58:45 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 08:58:45 | INFO | [TimerTask] 缓存图片消息: 2022627883
2025-07-31 08:58:45 | DEBUG | 收到消息: {'MsgId': 1215523630, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n已打卡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923527, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PUCctMTQ|v1_fWJ3DLwn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 已打卡', 'NewMsgId': 4471047109504898512, 'MsgSeq': 871413266}
2025-07-31 08:58:45 | INFO | 收到文本消息: 消息ID:1215523630 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:已打卡
2025-07-31 08:58:46 | DEBUG | 处理消息内容: '已打卡'
2025-07-31 08:58:46 | DEBUG | 消息内容 '已打卡' 不匹配任何命令，忽略
2025-07-31 09:02:03 | DEBUG | 收到消息: {'MsgId': 1279222678, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="8e737034a2a3db9a9ef006a6b4f0ac80" encryver="1" cdnthumbaeskey="8e737034a2a3db9a9ef006a6b4f0ac80" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" cdnthumblength="3451" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" length="888734" md5="d084a78564b3e550228a78a18bd3033e" hevc_mid_size="90850" originsourcemd5="f17f9eee63b632d644e47fe0cc413ea0">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMzA2MDgwMDAwMDIwMTAiLCJwZHFIYXNoIjoiNjJhZDM1YWExNDJhNTY2YjEy\nNTcyYTU2OWE0NWM2OGU3Mjg5YmU4ZDdjNjlkZTU4NmFkMzkyZGJkZWVhMDMxOSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923725, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>dbb9e165f1f1d69cb38e35b7e8b427c7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kTAIqjrg|v1_WoyY24Ph</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 7343132588271705725, 'MsgSeq': 871413267}
2025-07-31 09:02:03 | INFO | 收到图片消息: 消息ID:1279222678 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="8e737034a2a3db9a9ef006a6b4f0ac80" encryver="1" cdnthumbaeskey="8e737034a2a3db9a9ef006a6b4f0ac80" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" cdnthumblength="3451" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" length="888734" md5="d084a78564b3e550228a78a18bd3033e" hevc_mid_size="90850" originsourcemd5="f17f9eee63b632d644e47fe0cc413ea0"><secHashInfoBase64>eyJwaGFzaCI6IjUwMzA2MDgwMDAwMDIwMTAiLCJwZHFIYXNoIjoiNjJhZDM1YWExNDJhNTY2YjEyNTcyYTU2OWE0NWM2OGU3Mjg5YmU4ZDdjNjlkZTU4NmFkMzkyZGJkZWVhMDMxOSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 09:02:04 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 09:02:04 | INFO | [TimerTask] 缓存图片消息: 1279222678
2025-07-31 09:02:06 | DEBUG | 收到消息: {'MsgId': 2109900266, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n到了到了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923728, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9tWFJUmx|v1_dgrZwX5E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 到了到了', 'NewMsgId': 8477898734643166651, 'MsgSeq': 871413268}
2025-07-31 09:02:06 | INFO | 收到文本消息: 消息ID:2109900266 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:到了到了
2025-07-31 09:02:07 | DEBUG | 处理消息内容: '到了到了'
2025-07-31 09:02:07 | DEBUG | 消息内容 '到了到了' 不匹配任何命令，忽略
2025-07-31 09:03:21 | DEBUG | 收到消息: {'MsgId': 1875577093, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n到12：30'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923803, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_9zhXPmqQ|v1_w7c7WZ74</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6916279150307224826, 'MsgSeq': 871413269}
2025-07-31 09:03:21 | INFO | 收到文本消息: 消息ID:1875577093 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:到12：30
2025-07-31 09:03:21 | DEBUG | 处理消息内容: '到12：30'
2025-07-31 09:03:21 | DEBUG | 消息内容 '到12：30' 不匹配任何命令，忽略
2025-07-31 09:03:24 | DEBUG | 收到消息: {'MsgId': 624077807, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="1c02d12e214d662e93e6186e790379bc" len="108466" productid="" androidmd5="1c02d12e214d662e93e6186e790379bc" androidlen="108466" s60v3md5="1c02d12e214d662e93e6186e790379bc" s60v3len="108466" s60v5md5="1c02d12e214d662e93e6186e790379bc" s60v5len="108466" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=1c02d12e214d662e93e6186e790379bc&amp;filekey=30440201010430302e02016e0402534804203163303264313265323134643636326539336536313836653739303337396263020301a7b2040d00000004627466730000000132&amp;hy=SH&amp;storeid=26810817d00066a5b613e5ea80000006e01004fb1534811b411b1569b2297c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=24528d6c3c55c43467f2f6d4e098f6c3&amp;filekey=30440201010430302e02016e0402534804203234353238643663336335356334333436376632663664346530393866366333020301a7c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26810817d00075ca9613e5ea80000006e02004fb2534811b411b1569b2299b&amp;ef=2&amp;bizid=1022" aeskey="7de57dbbd59948c292545d3782872a63" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=fc7b3f89f3542ce0efe3dbb73c03141d&amp;filekey=3043020101042f302d02016e040253480420666337623366383966333534326365306566653364626237336330333134316402021150040d00000004627466730000000132&amp;hy=SH&amp;storeid=26810817d00081447613e5ea80000006e03004fb3534811b411b1569b229a8&amp;ef=3&amp;bizid=1022" externmd5="c9f1f72f4095a8be5b72925403bdd85a" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923805, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_nhGiRUEo|v1_U49yfTAN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6091972516234779156, 'MsgSeq': 871413270}
2025-07-31 09:03:24 | INFO | 收到表情消息: 消息ID:624077807 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:1c02d12e214d662e93e6186e790379bc 大小:108466
2025-07-31 09:03:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6091972516234779156
2025-07-31 09:04:22 | DEBUG | 收到消息: {'MsgId': 2085217213, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="395247175fb40011f579c10ee6216fb4" cdnvideourl="3057020100044b304902010002046b50398802032f9ea10204137928b6020468899bfe042465666634353464382d313830312d343563322d393834632d3631653166616334633737630204052408040201000405004c543d00" cdnthumbaeskey="395247175fb40011f579c10ee6216fb4" cdnthumburl="3057020100044b304902010002046b50398802032f9ea10204137928b6020468899bfe042465666634353464382d313830312d343563322d393834632d3631653166616334633737630204052408040201000405004c543d00" length="20984902" playlength="133" cdnthumblength="15308" cdnthumbwidth="360" cdnthumbheight="331" fromusername="zuoledd" md5="cd5d5b08c43dfe07064457f59744d949" newmd5="cef45802cd523dac9b061794b839751b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923863, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),wxid_quqvazin4e1j22,wxid_mrku4vbyrhpg22,zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>ef158964e7e2f43be5186fb80a8a49d6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xUM4nOfM|v1_IxHGOFGx</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一段视频', 'NewMsgId': 1443495672858732065, 'MsgSeq': 871413271}
2025-07-31 09:04:22 | INFO | 收到视频消息: 消息ID:2085217213 来自:48097389945@chatroom 发送人:zuoledd XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="395247175fb40011f579c10ee6216fb4" cdnvideourl="3057020100044b304902010002046b50398802032f9ea10204137928b6020468899bfe042465666634353464382d313830312d343563322d393834632d3631653166616334633737630204052408040201000405004c543d00" cdnthumbaeskey="395247175fb40011f579c10ee6216fb4" cdnthumburl="3057020100044b304902010002046b50398802032f9ea10204137928b6020468899bfe042465666634353464382d313830312d343563322d393834632d3631653166616334633737630204052408040201000405004c543d00" length="20984902" playlength="133" cdnthumblength="15308" cdnthumbwidth="360" cdnthumbheight="331" fromusername="zuoledd" md5="cd5d5b08c43dfe07064457f59744d949" newmd5="cef45802cd523dac9b061794b839751b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-31 09:05:05 | DEBUG | 收到消息: {'MsgId': 535168978, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n打工人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923907, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1r8CIj+F|v1_xxY13QgK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 打工人', 'NewMsgId': 5078867206871375388, 'MsgSeq': 871413272}
2025-07-31 09:05:05 | INFO | 收到文本消息: 消息ID:535168978 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:打工人
2025-07-31 09:05:06 | DEBUG | 处理消息内容: '打工人'
2025-07-31 09:05:06 | DEBUG | 消息内容 '打工人' 不匹配任何命令，忽略
2025-07-31 09:05:12 | DEBUG | 收到消息: {'MsgId': 801755560, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_srknxij3jka022:\n<msg><emoji fromusername = "wxid_srknxij3jka022" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="d19472081726fe106ede18efbcf01fc3" len = "79230" productid="" androidmd5="d19472081726fe106ede18efbcf01fc3" androidlen="79230" s60v3md5 = "d19472081726fe106ede18efbcf01fc3" s60v3len="79230" s60v5md5 = "d19472081726fe106ede18efbcf01fc3" s60v5len="79230" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=d19472081726fe106ede18efbcf01fc3&amp;filekey=30440201010430302e02016e0402534804206431393437323038313732366665313036656465313865666263663031666333020301357e040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688ac149000502f9f33b8a710000006e01004fb153481118a17156b131805&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=c6deedad51b7c96f9642eee76be499c2&amp;filekey=30440201010430302e02016e04025348042063366465656461643531623763393666393634326565653736626534393963320203013580040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688ac1490005b529f33b8a710000006e02004fb253481118a17156b13181c&amp;ef=2&amp;bizid=1022" aeskey= "51285340f8de491ba657194a26598131" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=d3733fa3c471b634efb6bcf6e6915b37&amp;filekey=30440201010430302e02016e0402534804206433373333666133633437316236333465666236626366366536393135623337020300b520040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688ac14900069831f33b8a710000006e03004fb353481118a17156b131833&amp;ef=3&amp;bizid=1022" externmd5 = "6fc3501948561fcbd2778d8936491e91" width= "750" height= "750" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923913, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_R4LqXzbe|v1_vZaSZChL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一个表情', 'NewMsgId': 1498462987288341278, 'MsgSeq': 871413273}
2025-07-31 09:05:12 | INFO | 收到表情消息: 消息ID:801755560 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 MD5:d19472081726fe106ede18efbcf01fc3 大小:79230
2025-07-31 09:05:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1498462987288341278
2025-07-31 09:05:57 | DEBUG | 收到消息: {'MsgId': 1629407554, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="1af3e0f6b79ca9f09dd632fe21d35e39" len = "298285" productid="" androidmd5="1af3e0f6b79ca9f09dd632fe21d35e39" androidlen="298285" s60v3md5 = "1af3e0f6b79ca9f09dd632fe21d35e39" s60v3len="298285" s60v5md5 = "1af3e0f6b79ca9f09dd632fe21d35e39" s60v5len="298285" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=1af3e0f6b79ca9f09dd632fe21d35e39&amp;filekey=30440201010430302e02016e04025348042031616633653066366237396361396630396464363332666532316433356533390203048d2d040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000ac3c650b1a01c0000006e01004fb15348258f31715737dbe4c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=5d3305731f754042a98ddb1138ffe982&amp;filekey=30440201010430302e02016e04025348042035643333303537333166373534303432613938646462313133386666653938320203048d30040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000bdda150b1a01c0000006e02004fb25348258f31715737dbe67&amp;ef=2&amp;bizid=1022" aeskey= "d75882876af84311b003a486e3769a11" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=278ab8e907ad639084160d2e576dbeec&amp;filekey=30440201010430302e02016e0402534804203237386162386539303761643633393038343136306432653537366462656563020300b4e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000d2a2750b1a01c0000006e03004fb35348258f31715737dbe7b&amp;ef=3&amp;bizid=1022" externmd5 = "f730aeab514f4c8168d9b61ea58d7220" width= "396" height= "396" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923959, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_W6uO7dCM|v1_MQUdkfrQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 4018354487553563183, 'MsgSeq': 871413274}
2025-07-31 09:05:57 | INFO | 收到表情消息: 消息ID:1629407554 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:1af3e0f6b79ca9f09dd632fe21d35e39 大小:298285
2025-07-31 09:05:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4018354487553563183
2025-07-31 09:05:58 | DEBUG | 收到消息: {'MsgId': 609854979, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="21c07f7359a8379f987a9968d63c9558" len = "505389" productid="" androidmd5="21c07f7359a8379f987a9968d63c9558" androidlen="505389" s60v3md5 = "21c07f7359a8379f987a9968d63c9558" s60v3len="505389" s60v5md5 = "21c07f7359a8379f987a9968d63c9558" s60v5len="505389" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=21c07f7359a8379f987a9968d63c9558&amp;filekey=30440201010430302e02016e0402534804203231633037663733353961383337396639383761393936386436336339353538020307b62d040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93b000e0f1d4dfca1a00000006e01004fb15348255e11b15057dea29&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=825915b12167b72e3532d5dcc2730a3a&amp;filekey=30440201010430302e02016e0402534804203832353931356231323136376237326533353332643564636332373330613361020307b630040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93b000f30254dfca1a00000006e02004fb25348255e11b15057dea39&amp;ef=2&amp;bizid=1022" aeskey= "b41d8bdd4d9b4a0e8fcf2f33f6367963" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a2250ef67e5574177befb4ac0e6e067c&amp;filekey=30440201010430302e02016e04025348042061323235306566363765353537343137376265666234616330653665303637630203019310040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93c0001238e4dfca1a00000006e03004fb35348255e11b15057dea50&amp;ef=3&amp;bizid=1022" externmd5 = "d8e1a9b7374a6a2bf1ac9c0ee0f2f16e" width= "313" height= "313" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923960, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_T42SLoQQ|v1_Yg5wz7q0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 6217474081622772060, 'MsgSeq': 871413275}
2025-07-31 09:05:58 | INFO | 收到表情消息: 消息ID:609854979 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:21c07f7359a8379f987a9968d63c9558 大小:505389
2025-07-31 09:05:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6217474081622772060
2025-07-31 09:06:06 | DEBUG | 收到消息: {'MsgId': 1696802754, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n打工人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923968, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9+iqvWCs|v1_q/qpnQrz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 打工人', 'NewMsgId': 204161557568456876, 'MsgSeq': 871413276}
2025-07-31 09:06:06 | INFO | 收到文本消息: 消息ID:1696802754 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:打工人
2025-07-31 09:06:07 | DEBUG | 处理消息内容: '打工人'
2025-07-31 09:06:07 | DEBUG | 消息内容 '打工人' 不匹配任何命令，忽略
2025-07-31 09:06:10 | DEBUG | 收到消息: {'MsgId': 713257297, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n打工虽然不易，但只要相信自己，努力拼搏，总有一天会实现自己的梦想和目标。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923971, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_YtCnexXy|v1_T+FHhAGc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 打工虽然不易，但只要相信自己，努力拼搏，总有一天会实现自己的...', 'NewMsgId': 7081611598758178941, 'MsgSeq': 871413277}
2025-07-31 09:06:10 | INFO | 收到文本消息: 消息ID:713257297 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:打工虽然不易，但只要相信自己，努力拼搏，总有一天会实现自己的梦想和目标。
2025-07-31 09:06:10 | DEBUG | 处理消息内容: '打工虽然不易，但只要相信自己，努力拼搏，总有一天会实现自己的梦想和目标。'
2025-07-31 09:06:10 | DEBUG | 消息内容 '打工虽然不易，但只要相信自己，努力拼搏，总有一天会实现自己的梦想和目标。' 不匹配任何命令，忽略
2025-07-31 09:06:13 | DEBUG | 收到消息: {'MsgId': 1769328100, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 打工人</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>6217474081622772060</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_wlnzvr8ivgd422:1753923960039:0:21c07f7359a8379f987a9968d63c9558::0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753923960</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923972, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a59fca2e2af898ac1a5d2b2cecd0b09a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kC/g6HVW|v1_WXZRmb/l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 命名 打工人', 'NewMsgId': 3722539381699309374, 'MsgSeq': 871413278}
2025-07-31 09:06:13 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-31 09:06:13 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 09:06:13 | INFO | 收到引用消息: 消息ID:1769328100 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:命名 打工人 引用类型:47
2025-07-31 09:06:13 | INFO | [DouBaoImageToImage] 收到引用消息: 命名 打工人
2025-07-31 09:06:13 | INFO | 成功保存表情映射文件，共 547 条记录
2025-07-31 09:06:14 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:表情已添加触发词：打工人
2025-07-31 09:06:14 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 09:06:14 | INFO |   - 消息内容: 命名 打工人
2025-07-31 09:06:14 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 09:06:14 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-31 09:06:14 | INFO |   - 引用信息: {'MsgType': 47, 'Content': 'wxid_wlnzvr8ivgd422:1753923960039:0:21c07f7359a8379f987a9968d63c9558::0\n', 'Msgid': '6217474081622772060', 'NewMsgId': '6217474081622772060', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚', 'MsgSource': None, 'Createtime': '1753923960', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-31 09:06:14 | INFO |   - 引用消息ID: 
2025-07-31 09:06:14 | INFO |   - 引用消息类型: 
2025-07-31 09:06:14 | INFO |   - 引用消息内容: wxid_wlnzvr8ivgd422:1753923960039:0:21c07f7359a8379f987a9968d63c9558::0

2025-07-31 09:06:14 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-31 09:06:21 | DEBUG | 收到消息: {'MsgId': 53465282, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n打工人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923983, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_l+kowdUG|v1_LTcA9ek7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 打工人', 'NewMsgId': 3159107553589381469, 'MsgSeq': 871413281}
2025-07-31 09:06:21 | INFO | 收到文本消息: 消息ID:53465282 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:打工人
2025-07-31 09:06:22 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:21c07f7359a8379f987a9968d63c9558 总长度:9992069
2025-07-31 09:06:22 | DEBUG | 处理消息内容: '打工人'
2025-07-31 09:06:22 | DEBUG | 消息内容 '打工人' 不匹配任何命令，忽略
2025-07-31 09:06:24 | DEBUG | 收到消息: {'MsgId': 1685722597, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n在职场中修炼内功，打造属于自己的核心竞争力。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923985, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OIZj87rx|v1_uaGrcyCR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 在职场中修炼内功，打造属于自己的核心竞争力。', 'NewMsgId': 414798131141967236, 'MsgSeq': 871413284}
2025-07-31 09:06:24 | INFO | 收到文本消息: 消息ID:1685722597 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:在职场中修炼内功，打造属于自己的核心竞争力。
2025-07-31 09:06:25 | DEBUG | 处理消息内容: '在职场中修炼内功，打造属于自己的核心竞争力。'
2025-07-31 09:06:25 | DEBUG | 消息内容 '在职场中修炼内功，打造属于自己的核心竞争力。' 不匹配任何命令，忽略
2025-07-31 09:06:44 | DEBUG | 收到消息: {'MsgId': 1759279692, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924006, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xHwYXLYm|v1_29rxa4DX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 渣男', 'NewMsgId': 2538176748197889431, 'MsgSeq': 871413285}
2025-07-31 09:06:44 | INFO | 收到文本消息: 消息ID:1759279692 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:渣男
2025-07-31 09:06:44 | DEBUG | 处理消息内容: '渣男'
2025-07-31 09:06:44 | DEBUG | 消息内容 '渣男' 不匹配任何命令，忽略
2025-07-31 09:07:06 | DEBUG | 收到消息: {'MsgId': 1169818546, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n这表情真丑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924028, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_6pH5Gr9L|v1_JHufugcM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6244882518807327032, 'MsgSeq': 871413286}
2025-07-31 09:07:06 | INFO | 收到文本消息: 消息ID:1169818546 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:这表情真丑
2025-07-31 09:07:06 | DEBUG | 处理消息内容: '这表情真丑'
2025-07-31 09:07:06 | DEBUG | 消息内容 '这表情真丑' 不匹配任何命令，忽略
2025-07-31 09:07:19 | DEBUG | 收到消息: {'MsgId': 2054491487, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n斩男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924041, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_5z2T968K|v1_2+5hJqEV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 斩男', 'NewMsgId': 3594382414921228604, 'MsgSeq': 871413287}
2025-07-31 09:07:19 | INFO | 收到文本消息: 消息ID:2054491487 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:斩男
2025-07-31 09:07:20 | DEBUG | 处理消息内容: '斩男'
2025-07-31 09:07:20 | DEBUG | 消息内容 '斩男' 不匹配任何命令，忽略
2025-07-31 09:07:22 | DEBUG | 收到消息: {'MsgId': 315576691, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924043, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_/W0BWrxc|v1_oW/Xxefg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 渣男', 'NewMsgId': 9214078789796545034, 'MsgSeq': 871413288}
2025-07-31 09:07:22 | INFO | 收到文本消息: 消息ID:315576691 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:渣男
2025-07-31 09:07:23 | DEBUG | 处理消息内容: '渣男'
2025-07-31 09:07:23 | DEBUG | 消息内容 '渣男' 不匹配任何命令，忽略
2025-07-31 09:07:26 | DEBUG | 收到消息: {'MsgId': 1524464336, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1870889322</msgid><newmsgid>3594382414921228604</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924045, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3184162144254683896, 'MsgSeq': 871413289}
2025-07-31 09:07:26 | DEBUG | 系统消息类型: revokemsg
2025-07-31 09:07:26 | INFO | 未知的系统消息类型: {'MsgId': 1524464336, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1870889322</msgid><newmsgid>3594382414921228604</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924045, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3184162144254683896, 'MsgSeq': 871413289, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
