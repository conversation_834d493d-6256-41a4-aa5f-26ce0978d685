2025-07-31 09:07:44 | SUCCESS | 读取主设置成功
2025-07-31 09:07:44 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-31 09:07:44 | INFO | 2025/07/31 09:07:44 GetRedisAddr: 127.0.0.1:6379
2025-07-31 09:07:44 | INFO | 2025/07/31 09:07:44 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-31 09:07:44 | INFO | 2025/07/31 09:07:44 Server start at :9000
2025-07-31 09:07:44 | SUCCESS | WechatAPI服务已启动
2025-07-31 09:07:45 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-31 09:07:45 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-31 09:07:45 | SUCCESS | 登录成功
2025-07-31 09:07:45 | SUCCESS | 已开启自动心跳
2025-07-31 09:07:45 | INFO | 成功加载表情映射文件，共 547 条记录
2025-07-31 09:07:45 | SUCCESS | 数据库初始化成功
2025-07-31 09:07:45 | SUCCESS | 定时任务已启动
2025-07-31 09:07:45 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-31 09:07:45 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 09:07:46 | INFO | 播客API初始化成功
2025-07-31 09:07:46 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 09:07:46 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-31 09:07:46 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-31 09:07:46 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-31 09:07:46 | INFO | [ChatSummary] 数据库初始化成功
2025-07-31 09:07:46 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-31 09:07:46 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-31 09:07:46 | DEBUG |   - 启用状态: True
2025-07-31 09:07:46 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-31 09:07:46 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-31 09:07:46 | DEBUG |   - Web ID: 7468716986638386703
2025-07-31 09:07:46 | DEBUG |   - Cookies配置: 已配置
2025-07-31 09:07:46 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-31 09:07:46 | DEBUG |   - 自然化响应: True
2025-07-31 09:07:46 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-31 09:07:46 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-31 09:07:46 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-31 09:07:46 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-31 09:07:46 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-31 09:07:46 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-31 09:07:46 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-31 09:07:46 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 09:07:46 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-31 09:07:46 | INFO | [RenameReminder] 开始启用插件...
2025-07-31 09:07:46 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-31 09:07:46 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-31 09:07:46 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-31 09:07:46 | INFO | 已设置检查间隔为 3600 秒
2025-07-31 09:07:46 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-31 09:07:47 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-31 09:07:47 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-31 09:07:47 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-31 09:07:47 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-31 09:07:48 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-31 09:07:48 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 09:07:48 | INFO | [yuanbao] 插件初始化完成
2025-07-31 09:07:48 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-31 09:07:48 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-31 09:07:48 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-31 09:07:48 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-31 09:07:48 | INFO | 处理堆积消息中
2025-07-31 09:07:48 | DEBUG | 接受到 1 条消息
2025-07-31 09:07:49 | SUCCESS | 处理堆积消息完毕
2025-07-31 09:07:49 | SUCCESS | 开始处理消息
2025-07-31 09:07:58 | DEBUG | 收到消息: {'MsgId': 228872838, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924080, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_allOLvBH|v1_dUmcpgLW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 6025388907971724607, 'MsgSeq': 871413291}
2025-07-31 09:07:58 | INFO | 收到文本消息: 消息ID:228872838 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-31 09:07:58 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753924078.62秒前, 等待时间: 0.00秒
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-31 09:07:58 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女'
2025-07-31 09:07:58 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7468716989062841895, Web ID: 7468716986638386703
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 1983
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 生成会话ID: 38175392407862498, 消息ID: ba0d6520-bcfb-11f0-a188-0d4dc47426d9
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 请求头: {'Host': 'www.doubao.com', 'Connection': 'keep-alive', 'x-flow-trace': '04-c717d4d9d29440e0-95f821f23a4748e4-01', 'sec-ch-ua-platform': '"Android"', 'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"', 'sec-ch-ua-mobile': '?1', 'Agw-Js-Conv': 'str, str', 'last-event-id': 'undefined', 'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36', 'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://www.doubao.com/chat/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'}
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": false, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "section_id": "381753924047862498", "conversation_id": "38175392407862498", "local_message_id": "ba0d6520-bcfb-11f0-a188-0d4dc47426d9"}
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7468716989062841895&device_plat...
2025-07-31 09:07:58 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 606 字符
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Thu, 31 Jul 2025 01:08:01 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '202507310908014636920E689DF31CE6EC', 'server-timing': 'inner; dur=219,tt_agw; dur=209', 'x-ms-token': '9jZcS_38Pn9bRxxUk_14vBzYAII98H0cnIOWut_HMRw4tLhV_dXyNPXw3WWzJMDQIpiM_2E_eaEwH7aCm2LPkI_yoB8kqLId067dHlrd', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f84ac7412e4f9dd892258b9a8542a090e19f9b1d14a4656a752a0935d38ae285e74a451e3d9f740cd2fc74f8db439367e21e0d9668fd95c41c7435aadca9a34d64a336db72fe22414e6f531022a6df9411a27ab8151c8141f835ec64a4c5fbced62', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-2507310908014636920E689DF31CE6EC-481BCCA678726EE9-00', 'x-tt-timestamp': '1753924081.513', 'via': 'cache7.cn7023[264,0]', 'timing-allow-origin': '*', 'eagleid': 'db999b1b17539240812745584e'}
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 252 字节
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 252 字节
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 244 字符
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 176
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 事件类型: 2005
2025-07-31 09:07:59 | WARNING | [DoubaoVideoSearch] 收到错误事件: {'code': 710020702, 'message': 'system error', 'error_detail': {'code': 710020702, 'locale': 'zh', 'message': '系统错误'}}
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 52
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 事件类型: 2003
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 收到结束事件
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 结束事件：返回文本结果
2025-07-31 09:07:59 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-31 09:07:59 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: text
2025-07-31 09:07:59 | WARNING | [DoubaoVideoSearch] 结果类型不是视频: text
2025-07-31 09:07:59 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:这个不行
2025-07-31 09:07:59 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.97秒
2025-07-31 09:07:59 | INFO | 成功加载表情映射文件，共 547 条记录
2025-07-31 09:07:59 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-31 09:07:59 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-31 09:08:05 | DEBUG | 收到消息: {'MsgId': 14417853, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924087, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ZxjeXf4k|v1_AeNTsLML</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 渣男', 'NewMsgId': 1370551839925093679, 'MsgSeq': 871413294}
2025-07-31 09:08:05 | INFO | 收到文本消息: 消息ID:14417853 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:渣男
2025-07-31 09:08:06 | DEBUG | 处理消息内容: '渣男'
2025-07-31 09:08:06 | DEBUG | 消息内容 '渣男' 不匹配任何命令，忽略
2025-07-31 09:08:08 | DEBUG | 收到消息: {'MsgId': 1748414804, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n太晚了，没车了我们只能在外面住了。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924089, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_52FYw8to|v1_1kqIBUPl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 太晚了，没车了我们只能在外面住了。', 'NewMsgId': 2993019393616867327, 'MsgSeq': 871413295}
2025-07-31 09:08:08 | INFO | 收到文本消息: 消息ID:1748414804 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:太晚了，没车了我们只能在外面住了。
2025-07-31 09:08:09 | DEBUG | 处理消息内容: '太晚了，没车了我们只能在外面住了。'
2025-07-31 09:08:09 | DEBUG | 消息内容 '太晚了，没车了我们只能在外面住了。' 不匹配任何命令，忽略
2025-07-31 09:08:12 | DEBUG | 收到消息: {'MsgId': 1660051435, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n@亮\u2005头像'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924090, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[last--exile]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_RXYnCtlb|v1_7GXlohvk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : @亮\u2005头像', 'NewMsgId': 5409968477290011683, 'MsgSeq': 871413296}
2025-07-31 09:08:12 | INFO | 收到文本消息: 消息ID:1660051435 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:['last--exile'] 内容:@亮 头像
2025-07-31 09:08:12 | DEBUG | 处理消息内容: '@亮 头像'
2025-07-31 09:08:12 | DEBUG | 消息内容 '@亮 头像' 不匹配任何命令，忽略
2025-07-31 09:08:15 | DEBUG | 收到消息: {'MsgId': 73645119, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1692630907</msgid><newmsgid>541740216687616646</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924089, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4853958804239544221, 'MsgSeq': 871413297}
2025-07-31 09:08:15 | DEBUG | 系统消息类型: revokemsg
2025-07-31 09:08:15 | INFO | 未知的系统消息类型: {'MsgId': 73645119, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1692630907</msgid><newmsgid>541740216687616646</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924089, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4853958804239544221, 'MsgSeq': 871413297, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-31 09:08:15 | DEBUG | 收到消息: {'MsgId': 249492791, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7d7d1271afbbde3fb88704cebb8a9541" encryver="1" cdnthumbaeskey="7d7d1271afbbde3fb88704cebb8a9541" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" cdnthumblength="10768" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" length="77977" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" hdlength="188988" md5="06fb7bf4c169e9d43b6c552db4d913b5" hevc_mid_size="77977" originsourcemd5="06fb7bf4c169e9d43b6c552db4d913b5">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxca25444c1e5649c5</appid>\n\t\t<version>4</version>\n\t\t<appname>iPhome Xi Max工程机</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924093, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>022ae2cb207d8d36301494da5d553a14_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MjwSRTf+|v1_xIzotUVq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 5665368759509897234, 'MsgSeq': 871413298}
2025-07-31 09:08:15 | INFO | 收到图片消息: 消息ID:249492791 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="7d7d1271afbbde3fb88704cebb8a9541" encryver="1" cdnthumbaeskey="7d7d1271afbbde3fb88704cebb8a9541" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" cdnthumblength="10768" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" length="77977" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" hdlength="188988" md5="06fb7bf4c169e9d43b6c552db4d913b5" hevc_mid_size="77977" originsourcemd5="06fb7bf4c169e9d43b6c552db4d913b5"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxca25444c1e5649c5</appid><version>4</version><appname>iPhome Xi Max工程机</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 09:08:16 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 09:08:16 | INFO | [TimerTask] 缓存图片消息: 249492791
2025-07-31 09:08:16 | DEBUG | 收到消息: {'MsgId': 562506974, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>2017538126</msgid><newmsgid>9214078789796545034</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924092, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 633501869144299793, 'MsgSeq': 871413299}
2025-07-31 09:08:16 | DEBUG | 系统消息类型: revokemsg
2025-07-31 09:08:16 | INFO | 未知的系统消息类型: {'MsgId': 562506974, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>2017538126</msgid><newmsgid>9214078789796545034</newmsgid><replacemsg><![CDATA["阿尼亚与她" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924092, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 633501869144299793, 'MsgSeq': 871413299, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-31 09:08:16 | DEBUG | 收到消息: {'MsgId': 2019305086, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sqbkq6p122x422:\n渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924097, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8zRxOadm|v1_SSEOYRsE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '大猪蹄子 : 渣男', 'NewMsgId': 5405482807009880923, 'MsgSeq': 871413300}
2025-07-31 09:08:16 | INFO | 收到文本消息: 消息ID:2019305086 来自:48097389945@chatroom 发送人:wxid_sqbkq6p122x422 @:[] 内容:渣男
2025-07-31 09:08:17 | DEBUG | 处理消息内容: '渣男'
2025-07-31 09:08:17 | DEBUG | 消息内容 '渣男' 不匹配任何命令，忽略
2025-07-31 09:08:20 | DEBUG | 收到消息: {'MsgId': 148226505, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n拿你当人的时候，你尽量走人道好吗。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924099, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qlxO9tXr|v1_Rd3TjH2R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 拿你当人的时候，你尽量走人道好吗。', 'NewMsgId': 1680476356361876916, 'MsgSeq': 871413301}
2025-07-31 09:08:20 | INFO | 收到文本消息: 消息ID:148226505 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:拿你当人的时候，你尽量走人道好吗。
2025-07-31 09:08:20 | DEBUG | 处理消息内容: '拿你当人的时候，你尽量走人道好吗。'
2025-07-31 09:08:20 | DEBUG | 消息内容 '拿你当人的时候，你尽量走人道好吗。' 不匹配任何命令，忽略
2025-07-31 09:08:23 | DEBUG | 收到消息: {'MsgId': 1794729389, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 渣男</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>5665368759509897234</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_lneb7n23o4lg12</chatusr>\n\t\t\t<displayname>ᡣ</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;022ae2cb207d8d36301494da5d553a14_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="77977" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GpxSI5iS|v1_06aWqu4o&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7d7d1271afbbde3fb88704cebb8a9541" encryver="1" cdnthumbaeskey="7d7d1271afbbde3fb88704cebb8a9541" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" cdnthumblength="10768" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" length="77977" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" hdlength="188988" md5="06fb7bf4c169e9d43b6c552db4d913b5" hevc_mid_size="77977" originsourcemd5="06fb7bf4c169e9d43b6c552db4d913b5"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wxca25444c1e5649c5&lt;/appid&gt;\n\t\t&lt;version&gt;4&lt;/version&gt;\n\t\t&lt;appname&gt;iPhome Xi Max工程机&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753924093</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924103, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>022ae2cb207d8d36301494da5d553a14_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_y1TwChna|v1_LNfB2lXq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 命名 渣男', 'NewMsgId': 97898011095510315, 'MsgSeq': 871413302}
2025-07-31 09:08:23 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-31 09:08:23 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 09:08:23 | INFO | 收到引用消息: 消息ID:1794729389 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:命名 渣男 引用类型:3
2025-07-31 09:08:24 | INFO | [DouBaoImageToImage] 收到引用消息: 命名 渣男
2025-07-31 09:08:24 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:请引用一个表情包进行命名
2025-07-31 09:08:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 09:08:24 | INFO |   - 消息内容: 命名 渣男
2025-07-31 09:08:24 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 09:08:24 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-31 09:08:24 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 渣男</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>5665368759509897234</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_lneb7n23o4lg12</chatusr>\n\t\t\t<displayname>ᡣ</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;022ae2cb207d8d36301494da5d553a14_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="77977" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GpxSI5iS|v1_06aWqu4o&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7d7d1271afbbde3fb88704cebb8a9541" encryver="1" cdnthumbaeskey="7d7d1271afbbde3fb88704cebb8a9541" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" cdnthumblength="10768" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" length="77977" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" hdlength="188988" md5="06fb7bf4c169e9d43b6c552db4d913b5" hevc_mid_size="77977" originsourcemd5="06fb7bf4c169e9d43b6c552db4d913b5"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wxca25444c1e5649c5&lt;/appid&gt;\n\t\t&lt;version&gt;4&lt;/version&gt;\n\t\t&lt;appname&gt;iPhome Xi Max工程机&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753924093</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '5665368759509897234', 'NewMsgId': '5665368759509897234', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'ᡣ', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>022ae2cb207d8d36301494da5d553a14_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="77977" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_GpxSI5iS|v1_06aWqu4o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753924093', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-31 09:08:24 | INFO |   - 引用消息ID: 
2025-07-31 09:08:24 | INFO |   - 引用消息类型: 
2025-07-31 09:08:24 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>命名 渣男</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>5665368759509897234</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>wxid_lneb7n23o4lg12</chatusr>
			<displayname>ᡣ</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;alnode&gt;
		&lt;fr&gt;2&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;022ae2cb207d8d36301494da5d553a14_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="77977" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;73&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_GpxSI5iS|v1_06aWqu4o&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="7d7d1271afbbde3fb88704cebb8a9541" encryver="1" cdnthumbaeskey="7d7d1271afbbde3fb88704cebb8a9541" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" cdnthumblength="10768" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" length="77977" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f514902046a3122750204688ac1fc042433663337336666622d663734662d346664302d383531322d643535613231623139613439020405252a010201000405004c51e500" hdlength="188988" md5="06fb7bf4c169e9d43b6c552db4d913b5" hevc_mid_size="77977" originsourcemd5="06fb7bf4c169e9d43b6c552db4d913b5"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;appinfo&gt;
		&lt;appid&gt;wxca25444c1e5649c5&lt;/appid&gt;
		&lt;version&gt;4&lt;/version&gt;
		&lt;appname&gt;iPhome Xi Max工程机&lt;/appname&gt;
		&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;
		&lt;messageaction /&gt;
		&lt;messageext /&gt;
		&lt;mediatagname /&gt;
	&lt;/appinfo&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753924093</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-31 09:08:24 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-31 09:08:30 | DEBUG | 收到消息: {'MsgId': 1746651616, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n这个是九色鹿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924112, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_XPpeYZHQ|v1_N4HLjSi4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 这个是九色鹿', 'NewMsgId': 8255902750378892812, 'MsgSeq': 871413305}
2025-07-31 09:08:30 | INFO | 收到文本消息: 消息ID:1746651616 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:这个是九色鹿
2025-07-31 09:08:31 | DEBUG | 处理消息内容: '这个是九色鹿'
2025-07-31 09:08:31 | DEBUG | 消息内容 '这个是九色鹿' 不匹配任何命令，忽略
2025-07-31 09:08:35 | DEBUG | 收到消息: {'MsgId': 262393960, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n#九色鹿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924117, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1wzeqMRK|v1_S48HEydg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : #九色鹿', 'NewMsgId': 3436422125089664884, 'MsgSeq': 871413306}
2025-07-31 09:08:35 | INFO | 收到文本消息: 消息ID:262393960 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:#九色鹿
2025-07-31 09:08:35 | DEBUG | 处理消息内容: '#九色鹿'
2025-07-31 09:08:35 | DEBUG | 消息内容 '#九色鹿' 不匹配任何命令，忽略
2025-07-31 09:08:40 | DEBUG | 收到消息: {'MsgId': 1992322127, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n九色鹿是一个非常著名的神话形象，源于古代佛教传说。它是一种神鹿，身上长有九种不同颜色的皮毛，象征着吉祥、美好和神圣。九色鹿常常出现在佛教的绘画、雕塑和文学作品中，通常用来象征智慧、慈悲和忠诚。在不同的文化背景下，九色鹿也有不同的故事和象征意义，但总体来说，它都是一个受人尊敬和崇拜的形象。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924122, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SNkMXW73|v1_h/CQYI62</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : 九色鹿是一个非常著名的神话形象，源于古代佛教传说。它是一种神...', 'NewMsgId': 5662789382317687017, 'MsgSeq': 871413307}
2025-07-31 09:08:40 | INFO | 收到文本消息: 消息ID:1992322127 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:九色鹿是一个非常著名的神话形象，源于古代佛教传说。它是一种神鹿，身上长有九种不同颜色的皮毛，象征着吉祥、美好和神圣。九色鹿常常出现在佛教的绘画、雕塑和文学作品中，通常用来象征智慧、慈悲和忠诚。在不同的文化背景下，九色鹿也有不同的故事和象征意义，但总体来说，它都是一个受人尊敬和崇拜的形象。
2025-07-31 09:08:41 | DEBUG | 处理消息内容: '九色鹿是一个非常著名的神话形象，源于古代佛教传说。它是一种神鹿，身上长有九种不同颜色的皮毛，象征着吉祥、美好和神圣。九色鹿常常出现在佛教的绘画、雕塑和文学作品中，通常用来象征智慧、慈悲和忠诚。在不同的文化背景下，九色鹿也有不同的故事和象征意义，但总体来说，它都是一个受人尊敬和崇拜的形象。'
2025-07-31 09:08:41 | DEBUG | 消息内容 '九色鹿是一个非常著名的神话形象，源于古代佛教传说。它是一种神鹿，身上长有九种不同颜色的皮毛，象征着吉祥、美好和神圣。九色鹿常常出现在佛教的绘画、雕塑和文学作品中，通常用来象征智慧、慈悲和忠诚。在不同的文化背景下，九色鹿也有不同的故事和象征意义，但总体来说，它都是一个受人尊敬和崇拜的形象。' 不匹配任何命令，忽略
2025-07-31 09:08:48 | DEBUG | 收到消息: {'MsgId': 1972263599, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="2614" bufid="0" aeskey="7a6a626c78656966786861696c72776a" voiceurl="3052020100044b304902010002049363814102033d14ba0204e73c949d0204688ac222042463333436613566382d343734662d346238372d626665312d64633839343631333365663502040528000f02010004001dc74187" voicemd5="4e6e7f20224518a55ff1f6f69593dbf2" clientmsgid="41386366366231333863396431366400500908073125d67e8f9b47a105" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924130, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MQtPbtL+|v1_m7d8htaf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 8517480355929851267, 'MsgSeq': 871413308}
2025-07-31 09:08:48 | INFO | 收到语音消息: 消息ID:1972263599 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="2614" bufid="0" aeskey="7a6a626c78656966786861696c72776a" voiceurl="3052020100044b304902010002049363814102033d14ba0204e73c949d0204688ac222042463333436613566382d343734662d346238372d626665312d64633839343631333365663502040528000f02010004001dc74187" voicemd5="4e6e7f20224518a55ff1f6f69593dbf2" clientmsgid="41386366366231333863396431366400500908073125d67e8f9b47a105" fromusername="xiaomaochong" /></msg>
2025-07-31 09:08:49 | DEBUG | 收到消息: {'MsgId': 105424356, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n删除表情 渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924131, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_YCnjpWei|v1_UofFNoLh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 删除表情 渣男', 'NewMsgId': 1077875286290591965, 'MsgSeq': 871413309}
2025-07-31 09:08:49 | INFO | 收到文本消息: 消息ID:105424356 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:删除表情 渣男
2025-07-31 09:08:50 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:未找到触发词：渣男
2025-07-31 09:08:50 | DEBUG | 处理消息内容: '删除表情 渣男'
2025-07-31 09:08:50 | DEBUG | 消息内容 '删除表情 渣男' 不匹配任何命令，忽略
2025-07-31 09:09:43 | DEBUG | 收到消息: {'MsgId': 285067873, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n变成表情包'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924184, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8InWJ7cq|v1_bYrb7OTa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 变成表情包', 'NewMsgId': 7248232128197030461, 'MsgSeq': 871413312}
2025-07-31 09:09:43 | INFO | 收到文本消息: 消息ID:285067873 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:变成表情包
2025-07-31 09:09:43 | DEBUG | 处理消息内容: '变成表情包'
2025-07-31 09:09:43 | DEBUG | 消息内容 '变成表情包' 不匹配任何命令，忽略
2025-07-31 09:09:48 | DEBUG | 收到消息: {'MsgId': 1358436640, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n才能命名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924190, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_p13n8y2l|v1_6wLQNRj5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 才能命名', 'NewMsgId': 5854429695231682300, 'MsgSeq': 871413313}
2025-07-31 09:09:48 | INFO | 收到文本消息: 消息ID:1358436640 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:才能命名
2025-07-31 09:09:48 | DEBUG | 处理消息内容: '才能命名'
2025-07-31 09:09:48 | DEBUG | 消息内容 '才能命名' 不匹配任何命令，忽略
2025-07-31 09:10:14 | DEBUG | 收到消息: {'MsgId': 512339062, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n渣男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924215, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ZJ3hOLla|v1_sPEZIEZs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 渣男', 'NewMsgId': 8052530081912414190, 'MsgSeq': 871413314}
2025-07-31 09:10:14 | INFO | 收到文本消息: 消息ID:512339062 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:渣男
2025-07-31 09:10:15 | DEBUG | 处理消息内容: '渣男'
2025-07-31 09:10:15 | DEBUG | 消息内容 '渣男' 不匹配任何命令，忽略
2025-07-31 09:10:17 | DEBUG | 收到消息: {'MsgId': 1106517992, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n我喜欢你，但我不想谈恋爱。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924218, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_U2uOcHIE|v1_8GJyKhpG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 我喜欢你，但我不想谈恋爱。', 'NewMsgId': 1553580545212429597, 'MsgSeq': 871413315}
2025-07-31 09:10:17 | INFO | 收到文本消息: 消息ID:1106517992 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:我喜欢你，但我不想谈恋爱。
2025-07-31 09:10:18 | DEBUG | 处理消息内容: '我喜欢你，但我不想谈恋爱。'
2025-07-31 09:10:18 | DEBUG | 消息内容 '我喜欢你，但我不想谈恋爱。' 不匹配任何命令，忽略
2025-07-31 09:10:20 | DEBUG | 收到消息: {'MsgId': 1371001862, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n苏苏 我喜欢你，但我不想谈恋爱。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924221, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PZeacNR1|v1_ejmYxWKT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 苏苏 我喜欢你，但我不想谈恋爱。', 'NewMsgId': 6593536357114099700, 'MsgSeq': *********}
2025-07-31 09:10:20 | INFO | 收到文本消息: 消息ID:1371001862 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:苏苏 我喜欢你，但我不想谈恋爱。
2025-07-31 09:10:21 | DEBUG | 处理消息内容: '苏苏 我喜欢你，但我不想谈恋爱。'
2025-07-31 09:10:21 | DEBUG | 消息内容 '苏苏 我喜欢你，但我不想谈恋爱。' 不匹配任何命令，忽略
2025-07-31 09:10:23 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n666'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924225, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_sAbggF8t|v1_rANack0s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 666', 'NewMsgId': 4853487459288307213, 'MsgSeq': 871413317}
2025-07-31 09:10:23 | INFO | 收到文本消息: 消息ID:********* 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:666
2025-07-31 09:10:23 | DEBUG | 处理消息内容: '666'
2025-07-31 09:10:23 | DEBUG | 消息内容 '666' 不匹配任何命令，忽略
2025-07-31 09:10:26 | DEBUG | 收到消息: {'MsgId': 516855230, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_srknxij3jka022:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10597" bufid="0" aeskey="67636d7276796d6f6370626973696f61" voiceurl="3052020100044b304902010002049c5da34302033d11fe02048ef765b40204688ac282042433643330306338662d623038322d346331362d386566612d38386463336130626232343202040524000f0201000400de714ff7" voicemd5="80800d045ea8367791b7b92f16bd99e5" clientmsgid="4139633438626536333063383334300025091007312516af88829e2103" fromusername="wxid_srknxij3jka022" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924226, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WlUGXWG7|v1_4CukISbD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一段语音', 'NewMsgId': 3502996834580556438, 'MsgSeq': 871413318}
2025-07-31 09:10:26 | INFO | 收到语音消息: 消息ID:516855230 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10597" bufid="0" aeskey="67636d7276796d6f6370626973696f61" voiceurl="3052020100044b304902010002049c5da34302033d11fe02048ef765b40204688ac282042433643330306338662d623038322d346331362d386566612d38386463336130626232343202040524000f0201000400de714ff7" voicemd5="80800d045ea8367791b7b92f16bd99e5" clientmsgid="4139633438626536333063383334300025091007312516af88829e2103" fromusername="wxid_srknxij3jka022" /></msg>
2025-07-31 09:10:27 | DEBUG | 收到消息: {'MsgId': 1594535624, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yby6o1jbfqyd12:\n获取女大图失败，请稍后再试！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753924228, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_yvZWMxD4|v1_zfNQBnkP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'DPbot : 获取女大图失败，请稍后再试！', 'NewMsgId': 7810872704984218326, 'MsgSeq': 871413319}
2025-07-31 09:10:27 | INFO | 收到文本消息: 消息ID:1594535624 来自:47325400669@chatroom 发送人:wxid_yby6o1jbfqyd12 @:[] 内容:获取女大图失败，请稍后再试！
2025-07-31 09:10:27 | DEBUG | 处理消息内容: '获取女大图失败，请稍后再试！'
2025-07-31 09:10:27 | DEBUG | 消息内容 '获取女大图失败，请稍后再试！' 不匹配任何命令，忽略
