[DoubaoVideoSearch]
enable = true
command = ["找视频", "搜视频", "视频搜索"]
admin_command = ["视频搜索状态", "视频搜索报告"]
command-format = """
🎬 豆包视频搜索插件使用说明：

1️⃣ 视频搜索：
找视频 <关键词> - 搜索相关视频
搜视频 <关键词> - 搜索相关视频
视频搜索 <关键词> - 搜索相关视频

2️⃣ 智能去重：
每次搜索同一关键词会返回不同的视频，避免重复

3️⃣ 管理员命令：
视频搜索状态 - 查看插件运行状态和性能统计
视频搜索报告 - 生成详细的性能报告

💡 示例：
找视频 搞笑
搜视频 美食
视频搜索 音乐
"""

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复

# API配置
[DoubaoVideoSearch.API]
cookies = """i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf"""

# 限流配置
[DoubaoVideoSearch.rate_limit]
tokens_per_second = 0.5  # 每秒生成的令牌数（1个令牌/2秒）
bucket_size = 5  # 令牌桶容量
doubao_chat.py