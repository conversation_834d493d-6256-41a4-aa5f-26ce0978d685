2025-07-31 10:15:52 | SUCCESS | 读取主设置成功
2025-07-31 10:15:52 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-31 10:15:52 | INFO | 2025/07/31 10:15:52 GetRedisAddr: 127.0.0.1:6379
2025-07-31 10:15:52 | INFO | 2025/07/31 10:15:52 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-31 10:15:52 | INFO | 2025/07/31 10:15:52 Server start at :9000
2025-07-31 10:15:52 | SUCCESS | WechatAPI服务已启动
2025-07-31 10:15:53 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-31 10:15:53 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-31 10:15:53 | SUCCESS | 登录成功
2025-07-31 10:15:53 | SUCCESS | 已开启自动心跳
2025-07-31 10:15:53 | INFO | 成功加载表情映射文件，共 547 条记录
2025-07-31 10:15:53 | SUCCESS | 数据库初始化成功
2025-07-31 10:15:53 | SUCCESS | 定时任务已启动
2025-07-31 10:15:53 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-31 10:15:53 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 10:15:54 | INFO | 播客API初始化成功
2025-07-31 10:15:54 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 10:15:54 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-31 10:15:54 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-31 10:15:54 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-31 10:15:55 | INFO | [ChatSummary] 数据库初始化成功
2025-07-31 10:15:55 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-31 10:15:55 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-31 10:15:55 | DEBUG |   - 启用状态: True
2025-07-31 10:15:55 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-31 10:15:55 | DEBUG |   - 设备ID: 7532989318484657699
2025-07-31 10:15:55 | DEBUG |   - Web ID: 7532989324985157172
2025-07-31 10:15:55 | DEBUG |   - Cookies配置: 已配置
2025-07-31 10:15:55 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-31 10:15:55 | DEBUG |   - 自然化响应: True
2025-07-31 10:15:55 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-31 10:15:55 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-31 10:15:55 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-31 10:15:55 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-31 10:15:55 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-31 10:15:55 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-31 10:15:55 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-31 10:15:55 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 10:15:55 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-31 10:15:55 | INFO | [RenameReminder] 开始启用插件...
2025-07-31 10:15:55 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-31 10:15:55 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-31 10:15:55 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-31 10:15:55 | INFO | 已设置检查间隔为 3600 秒
2025-07-31 10:15:55 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-31 10:15:55 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-31 10:15:56 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-31 10:15:56 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-31 10:15:56 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-31 10:15:56 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-31 10:15:56 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 10:15:56 | INFO | [yuanbao] 插件初始化完成
2025-07-31 10:15:56 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-31 10:15:56 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-31 10:15:56 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-31 10:15:56 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-31 10:15:56 | INFO | 处理堆积消息中
2025-07-31 10:15:57 | SUCCESS | 处理堆积消息完毕
2025-07-31 10:15:57 | SUCCESS | 开始处理消息
2025-07-31 10:16:01 | DEBUG | 收到消息: {'MsgId': 1205679889, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'scottuk:\n女大'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928162, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_+m8XjmwP|v1_oSHsqjJc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 's : 女大', 'NewMsgId': 2741241847226635311, 'MsgSeq': 871413561}
2025-07-31 10:16:01 | INFO | 收到文本消息: 消息ID:1205679889 来自:47325400669@chatroom 发送人:scottuk @:[] 内容:女大
2025-07-31 10:16:01 | INFO | 成功加载表情映射文件，共 547 条记录
2025-07-31 10:16:01 | DEBUG | 处理消息内容: '女大'
2025-07-31 10:16:01 | DEBUG | 消息内容 '女大' 不匹配任何命令，忽略
2025-07-31 10:16:03 | DEBUG | 收到消息: {'MsgId': 952729118, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yby6o1jbfqyd12:\n获取女大图失败，请稍后再试！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928164, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Ss9AO+N1|v1_I4QZT6r4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'DPbot : 获取女大图失败，请稍后再试！', 'NewMsgId': 4899378434695592834, 'MsgSeq': 871413562}
2025-07-31 10:16:03 | INFO | 收到文本消息: 消息ID:952729118 来自:47325400669@chatroom 发送人:wxid_yby6o1jbfqyd12 @:[] 内容:获取女大图失败，请稍后再试！
2025-07-31 10:16:03 | DEBUG | 处理消息内容: '获取女大图失败，请稍后再试！'
2025-07-31 10:16:03 | DEBUG | 消息内容 '获取女大图失败，请稍后再试！' 不匹配任何命令，忽略
2025-07-31 10:16:10 | DEBUG | 收到消息: {'MsgId': 382316776, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<type>51</type>\n\t\t<url>https://support.weixin.qq.com/update/</url>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<finderFeed>\n\t\t\t<objectId><![CDATA[14712502265767069930]]></objectId>\n\t\t\t<feedType><![CDATA[4]]></feedType>\n\t\t\t<nickname><![CDATA[流星live..]]></nickname>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/OicSPLcydObOtjkDCadHFBy1AqrdNRkj0gBa8oKP7icZjrHiaZU882rspv8J9Tibq3oic8pRibSEsMJOEuUtgDsdKfU8rf566TibMO3yOG9R7Hbh8b3k1icqMzAyqrGcMtnibicnqq/0]]></avatar>\n\t\t\t<desc><![CDATA[#祥嘞嘞版辞九门回忆#钱是英雄胆  老师教的我全忘了 但社会教的我一直铭记于心]]></desc>\n\t\t\t<mediaCount><![CDATA[1]]></mediaCount>\n\t\t\t<objectNonceId><![CDATA[14260343149052455178_0_25_12_3_1753928164797335_4fae2690-6db4-11f0-b998-b15c7eec63b8]]></objectNonceId>\n\t\t\t<liveId><![CDATA[0]]></liveId>\n\t\t\t<username><![CDATA[v2_060000231003b20faec8c7e6811ac2d3c605ec35b07732c5db2549defa771777d3bc62479fd8@finder]]></username>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<sourceCommentScene>25</sourceCommentScene>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<videoPlayDuration><![CDATA[51]]></videoPlayDuration>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQBSv2gNpTpvsZ4GEd8qsIUQDASg5c3e2ccF6ro9Mh1bnCoD2hFzHuYvkSznToiaEbWKpgCLkPyaM4ic5aGa716Q8c&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac]]></url>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<height><![CDATA[1080]]></height>\n\t\t\t\t\t<mediaType><![CDATA[4]]></mediaType>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width><![CDATA[1440]]></width>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId><![CDATA[]]></objectId>\n\t\t\t\t<objectNonceId><![CDATA[]]></objectNonceId>\n\t\t\t</megaVideo>\n\t\t\t<bizUsername><![CDATA[]]></bizUsername>\n\t\t\t<bizNickname><![CDATA[]]></bizNickname>\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2><![CDATA[]]></bizUsernameV2>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[ChJ3eGlkXzAzOTIxMDM5MjA5MTISFDQ5NzM1NDcxNDE5QGNoYXRyb29tIhIxODcyNjY3NDQ3NDM3MTAyNTEo3aGrxAY=]]></lastGMsgID>\n\t\t\t<shareBypData><![CDATA[]]></shareBypData>\n\t\t\t<isDebug>0</isDebug>\n\t\t\t<content_type>0</content_type>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"3-2-25-8aaf28e60d3d599f5ed8f658bf618ae71753928164603","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t</finderFeed>\n\t</appmsg>\n\t<fromusername>zuoledd</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928172, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>9a03c89dca6cd2a5a60541c07a89ec3b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xIBZEvvn|v1_qeIE2Pde</signature>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 389331201930068262, 'MsgSeq': 871413563}
2025-07-31 10:16:10 | DEBUG | 从群聊消息中提取发送者: zuoledd
2025-07-31 10:16:10 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>
		<type>51</type>
		<url>https://support.weixin.qq.com/update/</url>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<finderFeed>
			<objectId><![CDATA[14712502265767069930]]></objectId>
			<feedType><![CDATA[4]]></feedType>
			<nickname><![CDATA[流星live..]]></nickname>
			<avatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/OicSPLcydObOtjkDCadHFBy1AqrdNRkj0gBa8oKP7icZjrHiaZU882rspv8J9Tibq3oic8pRibSEsMJOEuUtgDsdKfU8rf566TibMO3yOG9R7Hbh8b3k1icqMzAyqrGcMtnibicnqq/0]]></avatar>
			<desc><![CDATA[#祥嘞嘞版辞九门回忆#钱是英雄胆  老师教的我全忘了 但社会教的我一直铭记于心]]></desc>
			<mediaCount><![CDATA[1]]></mediaCount>
			<objectNonceId><![CDATA[14260343149052455178_0_25_12_3_1753928164797335_4fae2690-6db4-11f0-b998-b15c7eec63b8]]></objectNonceId>
			<liveId><![CDATA[0]]></liveId>
			<username><![CDATA[v2_060000231003b20faec8c7e6811ac2d3c605ec35b07732c5db2549defa771777d3bc62479fd8@finder]]></username>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<authIconType>0</authIconType>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<sourceCommentScene>25</sourceCommentScene>
			<mediaList>
				<media>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></thumbUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<videoPlayDuration><![CDATA[51]]></videoPlayDuration>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQBSv2gNpTpvsZ4GEd8qsIUQDASg5c3e2ccF6ro9Mh1bnCoD2hFzHuYvkSznToiaEbWKpgCLkPyaM4ic5aGa716Q8c&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac]]></url>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></coverUrl>
					<height><![CDATA[1080]]></height>
					<mediaType><![CDATA[4]]></mediaType>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width><![CDATA[1440]]></width>
				</media>
			</mediaList>
			<megaVideo>
				<objectId><![CDATA[]]></objectId>
				<objectNonceId><![CDATA[]]></objectNonceId>
			</megaVideo>
			<bizUsername><![CDATA[]]></bizUsername>
			<bizNickname><![CDATA[]]></bizNickname>
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2><![CDATA[]]></bizUsernameV2>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<bizAuthIconType>0</bizAuthIconType>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[ChJ3eGlkXzAzOTIxMDM5MjA5MTISFDQ5NzM1NDcxNDE5QGNoYXRyb29tIhIxODcyNjY3NDQ3NDM3MTAyNTEo3aGrxAY=]]></lastGMsgID>
			<shareBypData><![CDATA[]]></shareBypData>
			<isDebug>0</isDebug>
			<content_type>0</content_type>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"3-2-25-8aaf28e60d3d599f5ed8f658bf618ae71753928164603","shareSrcScene":4}]]></finderShareExtInfo>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
		</finderFeed>
	</appmsg>
	<fromusername>zuoledd</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-31 10:16:10 | DEBUG | XML消息类型: 51
2025-07-31 10:16:10 | DEBUG | XML消息标题: 当前微信版本不支持展示该内容，请升级至最新版本。
2025-07-31 10:16:10 | DEBUG | XML消息URL: https://support.weixin.qq.com/update/
2025-07-31 10:16:10 | INFO | 未知的XML消息类型: 51
2025-07-31 10:16:10 | INFO | 消息标题: 当前微信版本不支持展示该内容，请升级至最新版本。
2025-07-31 10:16:10 | INFO | 消息描述: N/A
2025-07-31 10:16:10 | INFO | 消息URL: https://support.weixin.qq.com/update/
2025-07-31 10:16:10 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>
		<type>51</type>
		<url>https://support.weixin.qq.com/update/</url>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<finderFeed>
			<objectId><![CDATA[14712502265767069930]]></objectId>
			<feedType><![CDATA[4]]></feedType>
			<nickname><![CDATA[流星live..]]></nickname>
			<avatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/OicSPLcydObOtjkDCadHFBy1AqrdNRkj0gBa8oKP7icZjrHiaZU882rspv8J9Tibq3oic8pRibSEsMJOEuUtgDsdKfU8rf566TibMO3yOG9R7Hbh8b3k1icqMzAyqrGcMtnibicnqq/0]]></avatar>
			<desc><![CDATA[#祥嘞嘞版辞九门回忆#钱是英雄胆  老师教的我全忘了 但社会教的我一直铭记于心]]></desc>
			<mediaCount><![CDATA[1]]></mediaCount>
			<objectNonceId><![CDATA[14260343149052455178_0_25_12_3_1753928164797335_4fae2690-6db4-11f0-b998-b15c7eec63b8]]></objectNonceId>
			<liveId><![CDATA[0]]></liveId>
			<username><![CDATA[v2_060000231003b20faec8c7e6811ac2d3c605ec35b07732c5db2549defa771777d3bc62479fd8@finder]]></username>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<authIconType>0</authIconType>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<sourceCommentScene>25</sourceCommentScene>
			<mediaList>
				<media>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></thumbUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<videoPlayDuration><![CDATA[51]]></videoPlayDuration>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQBSv2gNpTpvsZ4GEd8qsIUQDASg5c3e2ccF6ro9Mh1bnCoD2hFzHuYvkSznToiaEbWKpgCLkPyaM4ic5aGa716Q8c&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac]]></url>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz7oZKmktIJdStsgicCtsOtM4dicBF7yUibu4a4aEQSbibnRIQCbWaxQlZqyVic3QAgpRiaLzqqD5vj1cAVaZeUVs9FK7A&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE38E2gibwFl2VBkofLLajiaS6cW4kjZYbNqBSSqlAWLZzEqgNySIcwbO0VaROsAMNdto5DyF6BjFA4Sk3896yDWmMm8OibCYib1ef4ouquicXng9HH06rBO6kgL6Adgg54u0gGKrXXkRvkSeG&ctsc=2-25]]></coverUrl>
					<height><![CDATA[1080]]></height>
					<mediaType><![CDATA[4]]></mediaType>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width><![CDATA[1440]]></width>
				</media>
			</mediaList>
			<megaVideo>
				<objectId><![CDATA[]]></objectId>
				<objectNonceId><![CDATA[]]></objectNonceId>
			</megaVideo>
			<bizUsername><![CDATA[]]></bizUsername>
			<bizNickname><![CDATA[]]></bizNickname>
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2><![CDATA[]]></bizUsernameV2>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<bizAuthIconType>0</bizAuthIconType>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[ChJ3eGlkXzAzOTIxMDM5MjA5MTISFDQ5NzM1NDcxNDE5QGNoYXRyb29tIhIxODcyNjY3NDQ3NDM3MTAyNTEo3aGrxAY=]]></lastGMsgID>
			<shareBypData><![CDATA[]]></shareBypData>
			<isDebug>0</isDebug>
			<content_type>0</content_type>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"3-2-25-8aaf28e60d3d599f5ed8f658bf618ae71753928164603","shareSrcScene":4}]]></finderShareExtInfo>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
		</finderFeed>
	</appmsg>
	<fromusername>zuoledd</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-31 10:16:17 | DEBUG | 收到消息: {'MsgId': 8671307, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'scottuk:\n女大能不能修一修啊 快乐没了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928179, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_9mUK0su+|v1_AKpoKOhx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 's : 女大能不能修一修啊 快乐没了', 'NewMsgId': 5471259265383499055, 'MsgSeq': 871413564}
2025-07-31 10:16:17 | INFO | 收到文本消息: 消息ID:8671307 来自:47325400669@chatroom 发送人:scottuk @:[] 内容:女大能不能修一修啊 快乐没了
2025-07-31 10:16:17 | DEBUG | 处理消息内容: '女大能不能修一修啊 快乐没了'
2025-07-31 10:16:17 | DEBUG | 消息内容 '女大能不能修一修啊 快乐没了' 不匹配任何命令，忽略
2025-07-31 10:16:39 | DEBUG | 收到消息: {'MsgId': 1091200473, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6a75fd17f50360fd58ca627f016476b3" encryver="1" cdnthumbaeskey="6a75fd17f50360fd58ca627f016476b3" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" cdnthumblength="6693" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" length="131734" md5="511fb8eded9e52c81dbdfb5974d81a22" originsourcemd5="b4b25919fce8a4bee539c549a2674018">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928201, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b0f20537187430d99ebcf7a471de8b0e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_gyJrE3FJ|v1_ywtLgRCv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 4352667248937279797, 'MsgSeq': 871413565}
2025-07-31 10:16:39 | INFO | 收到图片消息: 消息ID:1091200473 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="6a75fd17f50360fd58ca627f016476b3" encryver="1" cdnthumbaeskey="6a75fd17f50360fd58ca627f016476b3" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" cdnthumblength="6693" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" length="131734" md5="511fb8eded9e52c81dbdfb5974d81a22" originsourcemd5="b4b25919fce8a4bee539c549a2674018"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:16:40 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 10:16:40 | INFO | [TimerTask] 缓存图片消息: 1091200473
2025-07-31 10:16:49 | DEBUG | 收到消息: {'MsgId': 1636155168, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n五百五'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928210, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Xpenq/fK|v1_X/JbvN6M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 五百五', 'NewMsgId': 8335769595265019092, 'MsgSeq': 871413566}
2025-07-31 10:16:49 | INFO | 收到文本消息: 消息ID:1636155168 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:五百五
2025-07-31 10:16:49 | DEBUG | 处理消息内容: '五百五'
2025-07-31 10:16:49 | DEBUG | 消息内容 '五百五' 不匹配任何命令，忽略
2025-07-31 10:17:03 | DEBUG | 收到消息: {'MsgId': 2127888185, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n不贵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928225, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bWzOr/Zb|v1_ebRKN7OR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 不贵', 'NewMsgId': 4529183282426842569, 'MsgSeq': 871413567}
2025-07-31 10:17:03 | INFO | 收到文本消息: 消息ID:2127888185 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:不贵
2025-07-31 10:17:03 | DEBUG | 处理消息内容: '不贵'
2025-07-31 10:17:03 | DEBUG | 消息内容 '不贵' 不匹配任何命令，忽略
2025-07-31 10:17:18 | DEBUG | 收到消息: {'MsgId': 444956852, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n这是最新款啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928240, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PBbRSrmF|v1_UcNe/u37</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 这是最新款啊', 'NewMsgId': 1212681249618680418, 'MsgSeq': 871413568}
2025-07-31 10:17:18 | INFO | 收到文本消息: 消息ID:444956852 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:这是最新款啊
2025-07-31 10:17:18 | DEBUG | 处理消息内容: '这是最新款啊'
2025-07-31 10:17:18 | DEBUG | 消息内容 '这是最新款啊' 不匹配任何命令，忽略
2025-07-31 10:17:21 | DEBUG | 收到消息: {'MsgId': 1814695452, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我用不到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928242, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+U6DtGt2|v1_C/kApQUT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我用不到', 'NewMsgId': 2846093728063758550, 'MsgSeq': 871413569}
2025-07-31 10:17:21 | INFO | 收到文本消息: 消息ID:1814695452 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我用不到
2025-07-31 10:17:21 | DEBUG | 处理消息内容: '我用不到'
2025-07-31 10:17:21 | DEBUG | 消息内容 '我用不到' 不匹配任何命令，忽略
2025-07-31 10:17:23 | DEBUG | 收到消息: {'MsgId': 331170584, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n老猪666换个图库吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928243, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_PS+DARUM|v1_I7HxiKs6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 老猪666换个图库吧', 'NewMsgId': 1662365483127116183, 'MsgSeq': 871413570}
2025-07-31 10:17:23 | INFO | 收到文本消息: 消息ID:331170584 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:老猪666换个图库吧
2025-07-31 10:17:23 | DEBUG | 处理消息内容: '老猪666换个图库吧'
2025-07-31 10:17:23 | DEBUG | 消息内容 '老猪666换个图库吧' 不匹配任何命令，忽略
2025-07-31 10:17:28 | DEBUG | 收到消息: {'MsgId': 1364957653, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n质量不行，不快乐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928250, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_9g6QcCGt|v1_LZ0+p0oH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 质量不行，不快乐', 'NewMsgId': 6173051592372790513, 'MsgSeq': 871413571}
2025-07-31 10:17:28 | INFO | 收到文本消息: 消息ID:1364957653 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:质量不行，不快乐
2025-07-31 10:17:28 | DEBUG | 处理消息内容: '质量不行，不快乐'
2025-07-31 10:17:28 | DEBUG | 消息内容 '质量不行，不快乐' 不匹配任何命令，忽略
2025-07-31 10:17:30 | DEBUG | 收到消息: {'MsgId': 2022122590, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n666'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928252, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_HRobM/Qm|v1_FS845Mvo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 666', 'NewMsgId': 9171200730967012625, 'MsgSeq': 871413572}
2025-07-31 10:17:30 | INFO | 收到文本消息: 消息ID:2022122590 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:666
2025-07-31 10:17:30 | DEBUG | 处理消息内容: '666'
2025-07-31 10:17:30 | DEBUG | 消息内容 '666' 不匹配任何命令，忽略
2025-07-31 10:17:33 | DEBUG | 收到消息: {'MsgId': 1676546967, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n豆包API未返回有效内容，请检查日志。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928252, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_vx6TZF9R|v1_Qvqc1l55</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 豆包API未返回有效内容，请检查日志。', 'NewMsgId': 4668519620980364651, 'MsgSeq': 871413573}
2025-07-31 10:17:33 | INFO | 收到文本消息: 消息ID:1676546967 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:豆包API未返回有效内容，请检查日志。
2025-07-31 10:17:33 | DEBUG | 处理消息内容: '豆包API未返回有效内容，请检查日志。'
2025-07-31 10:17:33 | DEBUG | 消息内容 '豆包API未返回有效内容，请检查日志。' 不匹配任何命令，忽略
2025-07-31 10:17:35 | DEBUG | 收到消息: {'MsgId': 1652472684, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\nwatch gt 蓝牙版就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928253, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ce7jpq3F|v1_vQt6gIiH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : watch gt 蓝牙版就行了', 'NewMsgId': 5952715688614939773, 'MsgSeq': 871413574}
2025-07-31 10:17:35 | INFO | 收到文本消息: 消息ID:1652472684 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:watch gt 蓝牙版就行了
2025-07-31 10:17:36 | DEBUG | 处理消息内容: 'watch gt 蓝牙版就行了'
2025-07-31 10:17:36 | DEBUG | 消息内容 'watch gt 蓝牙版就行了' 不匹配任何命令，忽略
2025-07-31 10:17:38 | DEBUG | 收到消息: {'MsgId': 1892559216, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yby6o1jbfqyd12:\n获取女大图失败，请稍后再试！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928255, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_MvvK5/Fe|v1_6Mus4A42</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'DPbot : 获取女大图失败，请稍后再试！', 'NewMsgId': 4165074772226795403, 'MsgSeq': 871413575}
2025-07-31 10:17:38 | INFO | 收到文本消息: 消息ID:1892559216 来自:47325400669@chatroom 发送人:wxid_yby6o1jbfqyd12 @:[] 内容:获取女大图失败，请稍后再试！
2025-07-31 10:17:38 | DEBUG | 处理消息内容: '获取女大图失败，请稍后再试！'
2025-07-31 10:17:38 | DEBUG | 消息内容 '获取女大图失败，请稍后再试！' 不匹配任何命令，忽略
2025-07-31 10:17:43 | DEBUG | 收到消息: {'MsgId': 803355703, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4027860831167943559, 'MsgSeq': 871413576}
2025-07-31 10:17:43 | DEBUG | 系统消息类型: secmsg
2025-07-31 10:17:43 | INFO | 未知的系统消息类型: {'MsgId': 803355703, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4027860831167943559, 'MsgSeq': 871413576, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-07-31 10:17:43 | DEBUG | 收到消息: {'MsgId': 2082603638, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 407076493024158058, 'MsgSeq': 871413577}
2025-07-31 10:17:43 | DEBUG | 系统消息类型: secmsg
2025-07-31 10:17:43 | INFO | 未知的系统消息类型: {'MsgId': 2082603638, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 407076493024158058, 'MsgSeq': 871413577, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-07-31 10:17:44 | DEBUG | 收到消息: {'MsgId': 1016756660, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2549602466057706811, 'MsgSeq': 871413578}
2025-07-31 10:17:44 | DEBUG | 系统消息类型: secmsg
2025-07-31 10:17:44 | INFO | 未知的系统消息类型: {'MsgId': 1016756660, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2549602466057706811, 'MsgSeq': 871413578, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-07-31 10:17:44 | DEBUG | 收到消息: {'MsgId': 1971211552, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7166589499748014463, 'MsgSeq': 871413579}
2025-07-31 10:17:44 | DEBUG | 系统消息类型: secmsg
2025-07-31 10:17:44 | INFO | 未知的系统消息类型: {'MsgId': 1971211552, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>3427912396899822406</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[]]></fold-reduce>\n      <block-range>3</block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928262, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7166589499748014463, 'MsgSeq': 871413579, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-07-31 10:17:44 | DEBUG | 收到消息: {'MsgId': 1893871943, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="78787561697273676674716866796f68" encryver="0" cdnthumbaeskey="78787561697273676674716866796f68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" cdnthumblength="3236" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" length="53318" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" hdlength="258449" md5="cbca25c6b44292c3bb3bee9d290d8c9e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928265, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>779d06336710b4bdc18b2377a79d9df0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_UOE9rPwu|v1_Q98WOxtz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 1200008980300739741, 'MsgSeq': 871413580}
2025-07-31 10:17:44 | INFO | 收到图片消息: 消息ID:1893871943 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="78787561697273676674716866796f68" encryver="0" cdnthumbaeskey="78787561697273676674716866796f68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" cdnthumblength="3236" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" length="53318" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" hdlength="258449" md5="cbca25c6b44292c3bb3bee9d290d8c9e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:17:44 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 10:17:44 | INFO | [TimerTask] 缓存图片消息: 1893871943
2025-07-31 10:17:45 | DEBUG | 收到消息: {'MsgId': 1861721177, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n八豆#重置'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928266, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_QbTbvbZD|v1_jKGCZa6+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 八豆#重置', 'NewMsgId': 4470243023275105651, 'MsgSeq': 871413581}
2025-07-31 10:17:45 | INFO | 收到文本消息: 消息ID:1861721177 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:八豆#重置
2025-07-31 10:17:45 | DEBUG | 处理消息内容: '八豆#重置'
2025-07-31 10:17:45 | DEBUG | 消息内容 '八豆#重置' 不匹配任何命令，忽略
2025-07-31 10:17:48 | DEBUG | 收到消息: {'MsgId': 1676649035, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n会话已重置。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928269, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_AffTZLOa|v1_b7WV4vHx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 会话已重置。', 'NewMsgId': 1682697867783239914, 'MsgSeq': 871413582}
2025-07-31 10:17:48 | INFO | 收到文本消息: 消息ID:1676649035 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:会话已重置。
2025-07-31 10:17:48 | DEBUG | 处理消息内容: '会话已重置。'
2025-07-31 10:17:48 | DEBUG | 消息内容 '会话已重置。' 不匹配任何命令，忽略
2025-07-31 10:17:52 | DEBUG | 收到消息: {'MsgId': 1180793536, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n电子产品买新不买旧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928270, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_AL0wFPDo|v1_qTp/WUl/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 电子产品买新不买旧', 'NewMsgId': 8778451713279968014, 'MsgSeq': 871413583}
2025-07-31 10:17:52 | INFO | 收到文本消息: 消息ID:1180793536 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:电子产品买新不买旧
2025-07-31 10:17:53 | DEBUG | 处理消息内容: '电子产品买新不买旧'
2025-07-31 10:17:53 | DEBUG | 消息内容 '电子产品买新不买旧' 不匹配任何命令，忽略
2025-07-31 10:17:55 | DEBUG | 收到消息: {'MsgId': 1969252459, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n八豆死了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928271, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Z16bsAsp|v1_cALzNVOv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 八豆死了吗', 'NewMsgId': 7307515187303028223, 'MsgSeq': 871413584}
2025-07-31 10:17:55 | INFO | 收到文本消息: 消息ID:1969252459 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:八豆死了吗
2025-07-31 10:17:55 | DEBUG | 处理消息内容: '八豆死了吗'
2025-07-31 10:17:55 | DEBUG | 消息内容 '八豆死了吗' 不匹配任何命令，忽略
2025-07-31 10:17:57 | DEBUG | 收到消息: {'MsgId': 1299546025, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个要等国补 安徽省没上线啊</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>4352667248937279797</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<createtime>1753928201</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;alnode&gt;\n        &lt;fr&gt;4&lt;/fr&gt;\n    &lt;/alnode&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;b0f20537187430d99ebcf7a471de8b0e_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;imgmsg_pd cdnmidimgurl_size="131734" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;73&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_hJr6TWxH|v1_yoPsL3aO&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6a75fd17f50360fd58ca627f016476b3" encryver="1" cdnthumbaeskey="6a75fd17f50360fd58ca627f016476b3" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" cdnthumblength="6693" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" length="131734" md5="511fb8eded9e52c81dbdfb5974d81a22" originsourcemd5="b4b25919fce8a4bee539c549a2674018"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<displayname>小爱</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928276, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>0346da4e0152275e9cc369f583bfec26_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_6JE+OkCb|v1_YT6lclkt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 这个要等国补 安徽省没上线啊', 'NewMsgId': 6179307958538829155, 'MsgSeq': 871413585}
2025-07-31 10:17:57 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-31 10:17:57 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 10:17:57 | INFO | 收到引用消息: 消息ID:1299546025 来自:48097389945@chatroom 发送人:zll953369865 内容:这个要等国补 安徽省没上线啊 引用类型:3
2025-07-31 10:17:58 | INFO | [DouBaoImageToImage] 收到引用消息: 这个要等国补 安徽省没上线啊
2025-07-31 10:17:58 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 10:17:58 | INFO |   - 消息内容: 这个要等国补 安徽省没上线啊
2025-07-31 10:17:58 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 10:17:58 | INFO |   - 发送人: zll953369865
2025-07-31 10:17:58 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个要等国补 安徽省没上线啊</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>4352667248937279797</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<createtime>1753928201</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;alnode&gt;\n        &lt;fr&gt;4&lt;/fr&gt;\n    &lt;/alnode&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;b0f20537187430d99ebcf7a471de8b0e_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;imgmsg_pd cdnmidimgurl_size="131734" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;73&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_hJr6TWxH|v1_yoPsL3aO&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6a75fd17f50360fd58ca627f016476b3" encryver="1" cdnthumbaeskey="6a75fd17f50360fd58ca627f016476b3" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" cdnthumblength="6693" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" length="131734" md5="511fb8eded9e52c81dbdfb5974d81a22" originsourcemd5="b4b25919fce8a4bee539c549a2674018"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<displayname>小爱</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '4352667248937279797', 'NewMsgId': '4352667248937279797', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n    <alnode>\n        <fr>4</fr>\n    </alnode>\n    <sec_msg_node>\n        <uuid>b0f20537187430d99ebcf7a471de8b0e_</uuid>\n        <risk-file-flag />\n        <risk-file-md5-list />\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <imgmsg_pd cdnmidimgurl_size="131734" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" />\n    <silence>1</silence>\n    <membercount>73</membercount>\n    <signature>N0_V1_hJr6TWxH|v1_yoPsL3aO</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753928201', 'SenderWxid': 'zll953369865'}
2025-07-31 10:17:58 | INFO |   - 引用消息ID: 
2025-07-31 10:17:58 | INFO |   - 引用消息类型: 
2025-07-31 10:17:58 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>这个要等国补 安徽省没上线啊</title>
		<des />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<dataurl />
		<lowurl />
		<lowdataurl />
		<recorditem />
		<thumburl />
		<messageaction />
		<laninfo />
		<refermsg>
			<type>3</type>
			<svrid>4352667248937279797</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>xiaomaochong</chatusr>
			<createtime>1753928201</createtime>
			<msgsource>&lt;msgsource&gt;
    &lt;alnode&gt;
        &lt;fr&gt;4&lt;/fr&gt;
    &lt;/alnode&gt;
    &lt;sec_msg_node&gt;
        &lt;uuid&gt;b0f20537187430d99ebcf7a471de8b0e_&lt;/uuid&gt;
        &lt;risk-file-flag /&gt;
        &lt;risk-file-md5-list /&gt;
        &lt;alnode&gt;
            &lt;fr&gt;1&lt;/fr&gt;
        &lt;/alnode&gt;
    &lt;/sec_msg_node&gt;
    &lt;imgmsg_pd cdnmidimgurl_size="131734" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;
    &lt;silence&gt;1&lt;/silence&gt;
    &lt;membercount&gt;73&lt;/membercount&gt;
    &lt;signature&gt;N0_V1_hJr6TWxH|v1_yoPsL3aO&lt;/signature&gt;
    &lt;tmp_node&gt;
        &lt;publisher-id /&gt;
    &lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="6a75fd17f50360fd58ca627f016476b3" encryver="1" cdnthumbaeskey="6a75fd17f50360fd58ca627f016476b3" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" cdnthumblength="6693" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad208042432383132643064372d633335392d343334312d613430662d666234313039633765386632020405250a020201000405004c4dfd00" length="131734" md5="511fb8eded9e52c81dbdfb5974d81a22" originsourcemd5="b4b25919fce8a4bee539c549a2674018"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<displayname>小爱</displayname>
		</refermsg>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<aeskey />
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath />
			<username />
			<appid />
			<appservicetype>0</appservicetype>
		</weappinfo>
		<websearch />
	</appmsg>
	<fromusername>zll953369865</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-31 10:17:58 | INFO |   - 引用消息发送人: zll953369865
2025-07-31 10:18:01 | DEBUG | 收到消息: {'MsgId': 725602053, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n我还活着呢～ 如果你有任何问题需要帮助，不管是知识咨询、内容创作还是其他需求，都可以随时跟我说哦，我会尽力为你解答的～'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928283, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_CNWcy5Bw|v1_jSU/BhUb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 我还活着呢～ 如果你有任何问题需要帮助，不管是知识咨询、内容...', 'NewMsgId': 8785775119677622044, 'MsgSeq': 871413586}
2025-07-31 10:18:01 | INFO | 收到文本消息: 消息ID:725602053 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:我还活着呢～ 如果你有任何问题需要帮助，不管是知识咨询、内容创作还是其他需求，都可以随时跟我说哦，我会尽力为你解答的～
2025-07-31 10:18:01 | DEBUG | 处理消息内容: '我还活着呢～ 如果你有任何问题需要帮助，不管是知识咨询、内容创作还是其他需求，都可以随时跟我说哦，我会尽力为你解答的～'
2025-07-31 10:18:01 | DEBUG | 消息内容 '我还活着呢～ 如果你有任何问题需要帮助，不管是知识咨询、内容创作还是其他需求，都可以随时跟我说哦，我会尽力为你解答的～' 不匹配任何命令，忽略
2025-07-31 10:18:03 | DEBUG | 收到消息: {'MsgId': 970640042, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n你这个还不如小米手环'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928285, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_dRYvdYpD|v1_cZKVaVQ8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 你这个还不如小米手环', 'NewMsgId': 6821613279597679624, 'MsgSeq': 871413587}
2025-07-31 10:18:03 | INFO | 收到文本消息: 消息ID:970640042 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:你这个还不如小米手环
2025-07-31 10:18:04 | DEBUG | 处理消息内容: '你这个还不如小米手环'
2025-07-31 10:18:04 | DEBUG | 消息内容 '你这个还不如小米手环' 不匹配任何命令，忽略
2025-07-31 10:18:06 | DEBUG | 收到消息: {'MsgId': 1324787897, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我买那么新干嘛 '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928286, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vZhL2qrS|v1_2rayLmKA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我买那么新干嘛 ', 'NewMsgId': 7248239976899453832, 'MsgSeq': 871413588}
2025-07-31 10:18:06 | INFO | 收到文本消息: 消息ID:1324787897 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我买那么新干嘛 
2025-07-31 10:18:07 | DEBUG | 处理消息内容: '我买那么新干嘛'
2025-07-31 10:18:07 | DEBUG | 消息内容 '我买那么新干嘛' 不匹配任何命令，忽略
2025-07-31 10:18:13 | DEBUG | 收到消息: {'MsgId': 1361880850, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我那个比小米手环好太多了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928295, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8NTy1UOG|v1_DzKdNRQp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我那个比小米手环好太多了', 'NewMsgId': 1588003260352195568, 'MsgSeq': 871413589}
2025-07-31 10:18:13 | INFO | 收到文本消息: 消息ID:1361880850 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我那个比小米手环好太多了
2025-07-31 10:18:14 | DEBUG | 处理消息内容: '我那个比小米手环好太多了'
2025-07-31 10:18:14 | DEBUG | 消息内容 '我那个比小米手环好太多了' 不匹配任何命令，忽略
2025-07-31 10:18:16 | DEBUG | 收到消息: {'MsgId': 1720274398, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n兄弟'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928296, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OdVwLHsX|v1_b0wLIcX3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 兄弟', 'NewMsgId': 6121333959515870117, 'MsgSeq': 871413590}
2025-07-31 10:18:16 | INFO | 收到文本消息: 消息ID:1720274398 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:兄弟
2025-07-31 10:18:17 | DEBUG | 处理消息内容: '兄弟'
2025-07-31 10:18:17 | DEBUG | 消息内容 '兄弟' 不匹配任何命令，忽略
2025-07-31 10:18:21 | DEBUG | 收到消息: {'MsgId': 1459477373, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n新的续航和功能都增加了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928302, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mpYvtkBo|v1_N9zi1O7y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 新的续航和功能都增加了', 'NewMsgId': 899461841206846488, 'MsgSeq': 871413591}
2025-07-31 10:18:21 | INFO | 收到文本消息: 消息ID:1459477373 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:新的续航和功能都增加了
2025-07-31 10:18:21 | DEBUG | 处理消息内容: '新的续航和功能都增加了'
2025-07-31 10:18:21 | DEBUG | 消息内容 '新的续航和功能都增加了' 不匹配任何命令，忽略
2025-07-31 10:18:27 | DEBUG | 收到消息: {'MsgId': 1402900394, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n不知道  别乱说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928309, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PwwU8Ko8|v1_/BgY5EvD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 不知道  别乱说', 'NewMsgId': 1485327991690095969, 'MsgSeq': 871413592}
2025-07-31 10:18:27 | INFO | 收到文本消息: 消息ID:1402900394 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:不知道  别乱说
2025-07-31 10:18:28 | DEBUG | 处理消息内容: '不知道  别乱说'
2025-07-31 10:18:28 | DEBUG | 消息内容 '不知道  别乱说' 不匹配任何命令，忽略
2025-07-31 10:18:30 | DEBUG | 收到消息: {'MsgId': 954077814, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n小米手环轻便'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928311, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_BR6zIsYs|v1_80rK3RUG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 小米手环轻便', 'NewMsgId': 2090116429151321435, 'MsgSeq': 871413593}
2025-07-31 10:18:30 | INFO | 收到文本消息: 消息ID:954077814 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:小米手环轻便
2025-07-31 10:18:31 | DEBUG | 处理消息内容: '小米手环轻便'
2025-07-31 10:18:31 | DEBUG | 消息内容 '小米手环轻便' 不匹配任何命令，忽略
2025-07-31 10:18:33 | DEBUG | 收到消息: {'MsgId': 1341817861, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n续航都是21天啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928315, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_07FkGkmh|v1_C57aw6LD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 续航都是21天啊', 'NewMsgId': 444874573337972444, 'MsgSeq': 871413594}
2025-07-31 10:18:33 | INFO | 收到文本消息: 消息ID:1341817861 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:续航都是21天啊
2025-07-31 10:18:34 | DEBUG | 处理消息内容: '续航都是21天啊'
2025-07-31 10:18:34 | DEBUG | 消息内容 '续航都是21天啊' 不匹配任何命令，忽略
2025-07-31 10:18:36 | DEBUG | 收到消息: {'MsgId': 590551822, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_62fiham2pn7521:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不变</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1200008980300739741</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;779d06336710b4bdc18b2377a79d9df0_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="53318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_Jrty487g|v1_Aua/iuhE&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="78787561697273676674716866796f68" encryver="0" cdnthumbaeskey="78787561697273676674716866796f68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" cdnthumblength="3236" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" length="53318" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" hdlength="258449" md5="cbca25c6b44292c3bb3bee9d290d8c9e"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753928266</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_62fiham2pn7521</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928315, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>779d06336710b4bdc18b2377a79d9df0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_4rBj0GJK|v1_ZZObl04p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不...', 'NewMsgId': 1159764543724594718, 'MsgSeq': 871413595}
2025-07-31 10:18:36 | DEBUG | 从群聊消息中提取发送者: wxid_62fiham2pn7521
2025-07-31 10:18:36 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 10:18:36 | INFO | 收到引用消息: 消息ID:590551822 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 内容:八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不 引用类型:3
2025-07-31 10:18:36 | INFO | [DouBaoImageToImage] 收到引用消息: 八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不变
2025-07-31 10:18:36 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 10:18:36 | INFO |   - 消息内容: 八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不变
2025-07-31 10:18:36 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-31 10:18:36 | INFO |   - 发送人: wxid_62fiham2pn7521
2025-07-31 10:18:36 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不变</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1200008980300739741</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;779d06336710b4bdc18b2377a79d9df0_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="53318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_Jrty487g|v1_Aua/iuhE&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="78787561697273676674716866796f68" encryver="0" cdnthumbaeskey="78787561697273676674716866796f68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" cdnthumblength="3236" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" length="53318" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" hdlength="258449" md5="cbca25c6b44292c3bb3bee9d290d8c9e"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753928266</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_62fiham2pn7521</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '1200008980300739741', 'NewMsgId': '1200008980300739741', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '阿猪米德', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>779d06336710b4bdc18b2377a79d9df0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="53318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_Jrty487g|v1_Aua/iuhE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753928266', 'SenderWxid': 'wxid_62fiham2pn7521'}
2025-07-31 10:18:36 | INFO |   - 引用消息ID: 
2025-07-31 10:18:36 | INFO |   - 引用消息类型: 
2025-07-31 10:18:36 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>八豆参考图 生成不同的摄影师的风格，背景也要变化，人物保持不变</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>1200008980300739741</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_fh84okl6f5wp22</chatusr>
			<displayname>阿猪米德</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;779d06336710b4bdc18b2377a79d9df0_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="53318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_Jrty487g|v1_Aua/iuhE&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="78787561697273676674716866796f68" encryver="0" cdnthumbaeskey="78787561697273676674716866796f68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" cdnthumblength="3236" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" length="53318" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02047904b1a30204688ad24a042437313036396636322d303637372d343465642d623435372d3231306362366132653834310204052828010201000405004c4e620054381738" hdlength="258449" md5="cbca25c6b44292c3bb3bee9d290d8c9e"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753928266</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_62fiham2pn7521</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-31 10:18:36 | INFO |   - 引用消息发送人: wxid_62fiham2pn7521
2025-07-31 10:18:36 | DEBUG | 收到消息: {'MsgId': 2128091455, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n一样的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928317, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WOn4CXw+|v1_0J/37JZN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 一样的', 'NewMsgId': 3363035361891187456, 'MsgSeq': 871413596}
2025-07-31 10:18:36 | INFO | 收到文本消息: 消息ID:2128091455 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:一样的
2025-07-31 10:18:37 | DEBUG | 处理消息内容: '一样的'
2025-07-31 10:18:37 | DEBUG | 消息内容 '一样的' 不匹配任何命令，忽略
2025-07-31 10:18:56 | DEBUG | 收到消息: {'MsgId': 301294924, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2dac1dac18cdd303f6b0beb115cdeb27" encryver="1" cdnthumbaeskey="2dac1dac18cdd303f6b0beb115cdeb27" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad291042432306239326439642d666436362d346333632d623332382d346534363465363763333434020405250a020201000405004c550500" cdnthumblength="4276" cdnthumbheight="144" cdnthumbwidth="58" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad291042432306239326439642d666436362d346333632d623332382d346534363465363763333434020405250a020201000405004c550500" length="58453" md5="79094ae91a42f88910ccb6b2f6ed5dfe" hevc_mid_size="58453" originsourcemd5="6d425cfa40eae82bd7b52da8cf6f71b4">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928338, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>2e7c215a3aa3d6d6e52dfc90f5621091_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+qysOryO|v1_pLwsxRtM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 2223129013990137008, 'MsgSeq': 871413597}
2025-07-31 10:18:56 | INFO | 收到图片消息: 消息ID:301294924 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="2dac1dac18cdd303f6b0beb115cdeb27" encryver="1" cdnthumbaeskey="2dac1dac18cdd303f6b0beb115cdeb27" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad291042432306239326439642d666436362d346333632d623332382d346534363465363763333434020405250a020201000405004c550500" cdnthumblength="4276" cdnthumbheight="144" cdnthumbwidth="58" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad291042432306239326439642d666436362d346333632d623332382d346534363465363763333434020405250a020201000405004c550500" length="58453" md5="79094ae91a42f88910ccb6b2f6ed5dfe" hevc_mid_size="58453" originsourcemd5="6d425cfa40eae82bd7b52da8cf6f71b4"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:18:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 10:18:57 | INFO | [TimerTask] 缓存图片消息: 301294924
2025-07-31 10:19:09 | DEBUG | 收到消息: {'MsgId': 1434070998, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n22天'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928351, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PZ2k2RXd|v1_RDPBNhl+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 22天', 'NewMsgId': 1532921483919006522, 'MsgSeq': 871413598}
2025-07-31 10:19:09 | INFO | 收到文本消息: 消息ID:1434070998 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:22天
2025-07-31 10:19:09 | DEBUG | 处理消息内容: '22天'
2025-07-31 10:19:09 | DEBUG | 消息内容 '22天' 不匹配任何命令，忽略
2025-07-31 10:19:20 | DEBUG | 收到消息: {'MsgId': 1368030778, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n好的，我将为你生成不同摄影师风格且背景变化的图片，人物保持不变。首先尝试Ansel Adams的风光摄影风格，背景设为壮丽的山川景色。接下来尝试Richard Avedon的时尚肖像风格，背景将变为简约的纯色背景。然后尝试Steve McCurry的纪实摄影风格，背景设为充满生活气息的街头场景。最后尝试David LaChapelle的超现实主义风格，背景会设计成梦幻的色彩斑斓场景。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928362, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_RDSlWDKN|v1_uk9CdJvu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 好的，我将为你生成不同摄影师风格且背景变化的图片，人物保持不...', 'NewMsgId': 8098849257659332209, 'MsgSeq': 871413599}
2025-07-31 10:19:20 | INFO | 收到文本消息: 消息ID:1368030778 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:好的，我将为你生成不同摄影师风格且背景变化的图片，人物保持不变。首先尝试Ansel Adams的风光摄影风格，背景设为壮丽的山川景色。接下来尝试Richard Avedon的时尚肖像风格，背景将变为简约的纯色背景。然后尝试Steve McCurry的纪实摄影风格，背景设为充满生活气息的街头场景。最后尝试David LaChapelle的超现实主义风格，背景会设计成梦幻的色彩斑斓场景。
2025-07-31 10:19:20 | DEBUG | 处理消息内容: '好的，我将为你生成不同摄影师风格且背景变化的图片，人物保持不变。首先尝试Ansel Adams的风光摄影风格，背景设为壮丽的山川景色。接下来尝试Richard Avedon的时尚肖像风格，背景将变为简约的纯色背景。然后尝试Steve McCurry的纪实摄影风格，背景设为充满生活气息的街头场景。最后尝试David LaChapelle的超现实主义风格，背景会设计成梦幻的色彩斑斓场景。'
2025-07-31 10:19:20 | DEBUG | 消息内容 '好的，我将为你生成不同摄影师风格且背景变化的图片，人物保持不变。首先尝试Ansel Adams的风光摄影风格，背景设为壮丽的山川景色。接下来尝试Richard Avedon的时尚肖像风格，背景将变为简约的纯色背景。然后尝试Steve McCurry的纪实摄影风格，背景设为充满生活气息的街头场景。最后尝试David LaChapelle的超现实主义风格，背景会设计成梦幻的色彩斑斓场景。' 不匹配任何命令，忽略
2025-07-31 10:19:35 | DEBUG | 收到消息: {'MsgId': 2034173355, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="68656564667071696d6a65706b736774" encryver="0" cdnthumbaeskey="68656564667071696d6a65706b736774" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" cdnthumblength="3727" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" length="94800" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" hdlength="1986797" md5="d4fddb602149f7d286d6c2a39a070704">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928376, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>4545b6ba0acac3027b85e0d05fd14013_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_9+63NS8n|v1_yYxrmHgV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 4501585774965084428, 'MsgSeq': 871413600}
2025-07-31 10:19:35 | INFO | 收到图片消息: 消息ID:2034173355 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="68656564667071696d6a65706b736774" encryver="0" cdnthumbaeskey="68656564667071696d6a65706b736774" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" cdnthumblength="3727" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" length="94800" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02041f39949d0204688ad2b8042432666464323331392d376363662d346632362d383332612d3932636332663132383132350204052828010201000405004c51820054381738" hdlength="1986797" md5="d4fddb602149f7d286d6c2a39a070704"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:19:35 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 10:19:35 | INFO | [TimerTask] 缓存图片消息: 2034173355
2025-07-31 10:19:47 | DEBUG | 收到消息: {'MsgId': 1985249308, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6b77636c64746e79786e7a6763717575" encryver="0" cdnthumbaeskey="6b77636c64746e79786e7a6763717575" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" cdnthumblength="2701" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" length="42849" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" hdlength="1193315" md5="6697da8fd5a419c72f820c7bb7b9f941">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928388, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>4f13b1e2d8fa2f635d758f7648ee6ef7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_aT2TeIFY|v1_bZZHJw+o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 7659935208150021710, 'MsgSeq': 871413601}
2025-07-31 10:19:47 | INFO | 收到图片消息: 消息ID:1985249308 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="6b77636c64746e79786e7a6763717575" encryver="0" cdnthumbaeskey="6b77636c64746e79786e7a6763717575" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" cdnthumblength="2701" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" length="42849" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204be3f949d0204688ad2c4042464333933653665342d366335612d346234362d623961372d3737376564323831653566620204052828010201000405004c50560054381738" hdlength="1193315" md5="6697da8fd5a419c72f820c7bb7b9f941"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:19:47 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 10:19:47 | INFO | [TimerTask] 缓存图片消息: 1985249308
2025-07-31 10:19:52 | DEBUG | 收到消息: {'MsgId': 562237739, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你就为了多一天'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928394, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_J6pZsML6|v1_w/sgGq9R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你就为了多一天', 'NewMsgId': 7684316299392339527, 'MsgSeq': 871413602}
2025-07-31 10:19:52 | INFO | 收到文本消息: 消息ID:562237739 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你就为了多一天
2025-07-31 10:19:53 | DEBUG | 处理消息内容: '你就为了多一天'
2025-07-31 10:19:53 | DEBUG | 消息内容 '你就为了多一天' 不匹配任何命令，忽略
2025-07-31 10:19:55 | DEBUG | 收到消息: {'MsgId': 1453318750, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n买新款？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928396, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_77SkJeDT|v1_L8BSGWAW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 买新款？', 'NewMsgId': 5190819293286475087, 'MsgSeq': 871413603}
2025-07-31 10:19:55 | INFO | 收到文本消息: 消息ID:1453318750 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:买新款？
2025-07-31 10:19:56 | DEBUG | 处理消息内容: '买新款？'
2025-07-31 10:19:56 | DEBUG | 消息内容 '买新款？' 不匹配任何命令，忽略
2025-07-31 10:20:05 | DEBUG | 收到消息: {'MsgId': 1418827773, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="667673636b6c766d636c646d71727479" encryver="0" cdnthumbaeskey="667673636b6c766d636c646d71727479" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" cdnthumblength="4474" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" length="77166" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" hdlength="1865280" md5="785b54bfd0bbd59d86aec6b1337265dc">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928405, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>fcf608df407ab30b1512aca46cbeca42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_rgg2MABX|v1_3yJzKbPy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 8523785447067748351, 'MsgSeq': 871413604}
2025-07-31 10:20:05 | INFO | 收到图片消息: 消息ID:1418827773 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="667673636b6c766d636c646d71727479" encryver="0" cdnthumbaeskey="667673636b6c766d636c646d71727479" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" cdnthumblength="4474" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" length="77166" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" hdlength="1865280" md5="785b54bfd0bbd59d86aec6b1337265dc"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:20:05 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 10:20:05 | INFO | [TimerTask] 缓存图片消息: 1418827773
2025-07-31 10:20:10 | DEBUG | 收到消息: {'MsgId': 150365394, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="3c89a3a7c3d3a176c21ca63a3455875b" encryver="1" cdnthumbaeskey="3c89a3a7c3d3a176c21ca63a3455875b" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad2dc042436363031636339372d373030342d346132612d623630362d663131623533383261363634020405290a020201000405004c505500" cdnthumblength="3278" cdnthumbheight="120" cdnthumbwidth="70" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad2dc042436363031636339372d373030342d346132612d623630362d663131623533383261363634020405290a020201000405004c505500" length="35089" md5="68e666e603ee7fc349ec14590f879219" hevc_mid_size="35089" originsourcemd5="38720ffe2648ebfee9973f91e68a06f3">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928412, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>02655db579e779538ecf56b33f5585fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_junpdSFV|v1_NvpiBvt1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 9113218870593640417, 'MsgSeq': 871413605}
2025-07-31 10:20:10 | INFO | 收到图片消息: 消息ID:150365394 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="3c89a3a7c3d3a176c21ca63a3455875b" encryver="1" cdnthumbaeskey="3c89a3a7c3d3a176c21ca63a3455875b" cdnthumburl="3057020100044b304902010002049363814102032f51490204183122750204688ad2dc042436363031636339372d373030342d346132612d623630362d663131623533383261363634020405290a020201000405004c505500" cdnthumblength="3278" cdnthumbheight="120" cdnthumbwidth="70" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204183122750204688ad2dc042436363031636339372d373030342d346132612d623630362d663131623533383261363634020405290a020201000405004c505500" length="35089" md5="68e666e603ee7fc349ec14590f879219" hevc_mid_size="35089" originsourcemd5="38720ffe2648ebfee9973f91e68a06f3"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:20:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 10:20:11 | INFO | [TimerTask] 缓存图片消息: 150365394
2025-07-31 10:20:19 | DEBUG | 收到消息: {'MsgId': 1769351650, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="62786d7a6a746e726172677875626878" encryver="0" cdnthumbaeskey="62786d7a6a746e726172677875626878" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" cdnthumblength="3766" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" length="59973" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" hdlength="1758728" md5="4c35f4889cf8949dffa21827514b8cf0">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928420, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>f63113a392eb7d21e15a7a7ac6d4528e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_BYF6QFJz|v1_m6d6S2HG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 53551440253544563, 'MsgSeq': 871413606}
2025-07-31 10:20:19 | INFO | 收到图片消息: 消息ID:1769351650 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="62786d7a6a746e726172677875626878" encryver="0" cdnthumbaeskey="62786d7a6a746e726172677875626878" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" cdnthumblength="3766" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" length="59973" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cb39949d0204688ad2e4042438383434383936372d643535642d346331372d626131302d6566373563333037323938390204052828010201000405004c51e60054381738" hdlength="1758728" md5="4c35f4889cf8949dffa21827514b8cf0"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:20:19 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 10:20:19 | INFO | [TimerTask] 缓存图片消息: 1769351650
2025-07-31 10:20:20 | DEBUG | 收到消息: {'MsgId': 1575272663, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n- 放大: `$u|放大 1753928356 [1-4]`\n- 变清晰: `$q|清晰 1753928356 [1-4]`\n- 编辑: `$v|编辑 1753928356 [1-4] [提示词]`\n- 扩图: `$k|扩 1753928356 [1-4] [比例]`\n- 视频: `$s|动起来 1753928356 [1-4] [提示词]`\n- 重新生成: `$r|重新 1753928356`'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928422, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Q3UGoGma|v1_IY7/h7vB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : - 放大: `$u|放大 1753928356 [1-4]`\n- 变清晰: `$q|清晰 1753928356 [1-4]`\n- 编辑:...', 'NewMsgId': 5282385014965932103, 'MsgSeq': 871413607}
2025-07-31 10:20:20 | INFO | 收到文本消息: 消息ID:1575272663 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:- 放大: `$u|放大 1753928356 [1-4]`
- 变清晰: `$q|清晰 1753928356 [1-4]`
- 编辑: `$v|编辑 1753928356 [1-4] [提示词]`
- 扩图: `$k|扩 1753928356 [1-4] [比例]`
- 视频: `$s|动起来 1753928356 [1-4] [提示词]`
- 重新生成: `$r|重新 1753928356`
2025-07-31 10:20:20 | DEBUG | 处理消息内容: '- 放大: `$u|放大 1753928356 [1-4]`
- 变清晰: `$q|清晰 1753928356 [1-4]`
- 编辑: `$v|编辑 1753928356 [1-4] [提示词]`
- 扩图: `$k|扩 1753928356 [1-4] [比例]`
- 视频: `$s|动起来 1753928356 [1-4] [提示词]`
- 重新生成: `$r|重新 1753928356`'
2025-07-31 10:20:20 | DEBUG | 消息内容 '- 放大: `$u|放大 1753928356 [1-4]`
- 变清晰: `$q|清晰 1753928356 [1-4]`
- 编辑: `$v|编辑 1753928356 [1-4] [提示词]`
- 扩图: `$k|扩 1753928356 [1-4] [比例]`
- 视频: `$s|动起来 1753928356 [1-4] [提示词]`
- 重新生成: `$r|重新 1753928356`' 不匹配任何命令，忽略
2025-07-31 10:21:10 | DEBUG | 收到消息: {'MsgId': 2116841920, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n21天啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928472, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wXjqC8s7|v1_VYOzSa0m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 21天啊', 'NewMsgId': 3948863677879037308, 'MsgSeq': 871413608}
2025-07-31 10:21:10 | INFO | 收到文本消息: 消息ID:2116841920 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:21天啊
2025-07-31 10:21:11 | DEBUG | 处理消息内容: '21天啊'
2025-07-31 10:21:11 | DEBUG | 消息内容 '21天啊' 不匹配任何命令，忽略
2025-07-31 10:21:17 | DEBUG | 收到消息: {'MsgId': 442298269, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n该有的功能都有就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928479, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_lInAWQYN|v1_06IeocBH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 该有的功能都有就行了', 'NewMsgId': 7273409034340338576, 'MsgSeq': 871413609}
2025-07-31 10:21:17 | INFO | 收到文本消息: 消息ID:442298269 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:该有的功能都有就行了
2025-07-31 10:21:17 | DEBUG | 处理消息内容: '该有的功能都有就行了'
2025-07-31 10:21:17 | DEBUG | 消息内容 '该有的功能都有就行了' 不匹配任何命令，忽略
2025-07-31 10:21:27 | DEBUG | 收到消息: {'MsgId': 2095241865, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n这些手表买回来就贬值了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928489, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ej9u5vYH|v1_TeyxOVib</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 这些手表买回来就贬值了', 'NewMsgId': 3204530160021998460, 'MsgSeq': 871413610}
2025-07-31 10:21:27 | INFO | 收到文本消息: 消息ID:2095241865 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:这些手表买回来就贬值了
2025-07-31 10:21:28 | DEBUG | 处理消息内容: '这些手表买回来就贬值了'
2025-07-31 10:21:28 | DEBUG | 消息内容 '这些手表买回来就贬值了' 不匹配任何命令，忽略
2025-07-31 10:21:30 | DEBUG | 收到消息: {'MsgId': 76673118, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n买那么贵干嘛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928492, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_6c35M3ag|v1_UjYch430</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 买那么贵干嘛', 'NewMsgId': 4616187656427876555, 'MsgSeq': 871413611}
2025-07-31 10:21:30 | INFO | 收到文本消息: 消息ID:76673118 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:买那么贵干嘛
2025-07-31 10:21:30 | DEBUG | 处理消息内容: '买那么贵干嘛'
2025-07-31 10:21:30 | DEBUG | 消息内容 '买那么贵干嘛' 不匹配任何命令，忽略
2025-07-31 10:24:10 | DEBUG | 收到消息: {'MsgId': 1812558473, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiehuaping668:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>扣图痕迹也太明显了，ai不给力啊</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>8523785447067748351</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<content>wxid_fh84okl6f5wp22:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="667673636b6c766d636c646d71727479" encryver="0" cdnthumbaeskey="667673636b6c766d636c646d71727479" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" cdnthumblength="4474" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" length="77166" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" hdlength="1865280" md5="785b54bfd0bbd59d86aec6b1337265dc"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;fcf608df407ab30b1512aca46cbeca42_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnbigimgurl_size="1865280" cdnbigimgurl_pd_pri="30" cdnbigimgurl_pd="0" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_yzQqNAKx|v1_xsNbnLBz&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753928405</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>xiehuaping668</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928652, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>a89d46e452bec67bd1ebf58d9031e193_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_SVsHvd6/|v1_nkD4J9JT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你已被群主移出群聊 ر ر : 扣图痕迹也太明显了，ai不给力啊', 'NewMsgId': 6485243017934841699, 'MsgSeq': 871413612}
2025-07-31 10:24:10 | DEBUG | 从群聊消息中提取发送者: xiehuaping668
2025-07-31 10:24:10 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 10:24:10 | INFO | 收到引用消息: 消息ID:1812558473 来自:47325400669@chatroom 发送人:xiehuaping668 内容:扣图痕迹也太明显了，ai不给力啊 引用类型:3
2025-07-31 10:24:10 | INFO | [DouBaoImageToImage] 收到引用消息: 扣图痕迹也太明显了，ai不给力啊
2025-07-31 10:24:10 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 10:24:10 | INFO |   - 消息内容: 扣图痕迹也太明显了，ai不给力啊
2025-07-31 10:24:10 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-31 10:24:10 | INFO |   - 发送人: xiehuaping668
2025-07-31 10:24:10 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>扣图痕迹也太明显了，ai不给力啊</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>8523785447067748351</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<content>wxid_fh84okl6f5wp22:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="667673636b6c766d636c646d71727479" encryver="0" cdnthumbaeskey="667673636b6c766d636c646d71727479" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" cdnthumblength="4474" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" length="77166" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" hdlength="1865280" md5="785b54bfd0bbd59d86aec6b1337265dc"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;fcf608df407ab30b1512aca46cbeca42_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnbigimgurl_size="1865280" cdnbigimgurl_pd_pri="30" cdnbigimgurl_pd="0" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_yzQqNAKx|v1_xsNbnLBz&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753928405</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>xiehuaping668</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '8523785447067748351', 'NewMsgId': '8523785447067748351', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '阿猪米德', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>fcf608df407ab30b1512aca46cbeca42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnbigimgurl_size="1865280" cdnbigimgurl_pd_pri="30" cdnbigimgurl_pd="0" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_yzQqNAKx|v1_xsNbnLBz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753928405', 'SenderWxid': 'xiehuaping668'}
2025-07-31 10:24:10 | INFO |   - 引用消息ID: 
2025-07-31 10:24:10 | INFO |   - 引用消息类型: 
2025-07-31 10:24:10 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>扣图痕迹也太明显了，ai不给力啊</title>
		<des />
		<action />
		<type>57</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<aeskey />
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<refermsg>
			<type>3</type>
			<svrid>8523785447067748351</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_fh84okl6f5wp22</chatusr>
			<displayname>阿猪米德</displayname>
			<content>wxid_fh84okl6f5wp22:
&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="667673636b6c766d636c646d71727479" encryver="0" cdnthumbaeskey="667673636b6c766d636c646d71727479" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" cdnthumblength="4474" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" length="77166" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204cc10949d0204688ad2d6042432616137643962352d326633362d343166382d623166382d3166623436366132356233320204052828010201000405004c54a20054381738" hdlength="1865280" md5="785b54bfd0bbd59d86aec6b1337265dc"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;fcf608df407ab30b1512aca46cbeca42_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnbigimgurl_size="1865280" cdnbigimgurl_pd_pri="30" cdnbigimgurl_pd="0" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;signature&gt;N0_V1_yzQqNAKx|v1_xsNbnLBz&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1753928405</createtime>
		</refermsg>
	</appmsg>
	<fromusername>xiehuaping668</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-31 10:24:10 | INFO |   - 引用消息发送人: xiehuaping668
2025-07-31 10:25:21 | DEBUG | 收到消息: {'MsgId': 1163009703, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n 六百多都太便宜了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928723, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_M2KWrdO1|v1_p4tBLlh5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 :  六百多都太便宜了', 'NewMsgId': 6440280537657687340, 'MsgSeq': 871413613}
2025-07-31 10:25:21 | INFO | 收到文本消息: 消息ID:1163009703 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容: 六百多都太便宜了
2025-07-31 10:25:22 | DEBUG | 处理消息内容: '六百多都太便宜了'
2025-07-31 10:25:22 | DEBUG | 消息内容 '六百多都太便宜了' 不匹配任何命令，忽略
2025-07-31 10:25:55 | DEBUG | 收到消息: {'MsgId': 1246937999, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928757, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_aeYBOlHT|v1_3ncM2rnA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 哈哈', 'NewMsgId': 5661786189530524576, 'MsgSeq': 871413614}
2025-07-31 10:25:55 | INFO | 收到文本消息: 消息ID:1246937999 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:哈哈
2025-07-31 10:25:56 | DEBUG | 处理消息内容: '哈哈'
2025-07-31 10:25:56 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-07-31 10:26:01 | DEBUG | 收到消息: {'MsgId': 1333563885, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你买贵的就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928763, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_/iF9gGxz|v1_z6k36/fN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你买贵的就行了', 'NewMsgId': 738275958343881020, 'MsgSeq': 871413615}
2025-07-31 10:26:01 | INFO | 收到文本消息: 消息ID:1333563885 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你买贵的就行了
2025-07-31 10:26:01 | DEBUG | 处理消息内容: '你买贵的就行了'
2025-07-31 10:26:01 | DEBUG | 消息内容 '你买贵的就行了' 不匹配任何命令，忽略
2025-07-31 10:26:10 | DEBUG | 收到消息: {'MsgId': 469643676, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我以前用app watch 用了几年卖了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928773, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_RL2dDvH1|v1_4wXM6PMI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我以前用app watch 用了几年卖了', 'NewMsgId': 1226710453090560935, 'MsgSeq': 871413616}
2025-07-31 10:26:10 | INFO | 收到文本消息: 消息ID:469643676 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我以前用app watch 用了几年卖了
2025-07-31 10:26:11 | DEBUG | 处理消息内容: '我以前用app watch 用了几年卖了'
2025-07-31 10:26:11 | DEBUG | 消息内容 '我以前用app watch 用了几年卖了' 不匹配任何命令，忽略
2025-07-31 10:26:13 | DEBUG | 收到消息: {'MsgId': 347031051, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n没啥用'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928774, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_sRM01QjX|v1_jiyfhAqS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没啥用', 'NewMsgId': 8787530200633990452, 'MsgSeq': 871413617}
2025-07-31 10:26:13 | INFO | 收到文本消息: 消息ID:347031051 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:没啥用
2025-07-31 10:26:14 | DEBUG | 处理消息内容: '没啥用'
2025-07-31 10:26:14 | DEBUG | 消息内容 '没啥用' 不匹配任何命令，忽略
2025-07-31 10:26:16 | DEBUG | 收到消息: {'MsgId': 2075576122, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n700多卖出去的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928777, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OURsrRwo|v1_i8FP7cJ0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 700多卖出去的', 'NewMsgId': 8321631150875890564, 'MsgSeq': 871413618}
2025-07-31 10:26:16 | INFO | 收到文本消息: 消息ID:2075576122 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:700多卖出去的
2025-07-31 10:26:17 | DEBUG | 处理消息内容: '700多卖出去的'
2025-07-31 10:26:17 | DEBUG | 消息内容 '700多卖出去的' 不匹配任何命令，忽略
2025-07-31 10:26:28 | DEBUG | 收到消息: {'MsgId': 648100674, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n续航不如普通的手表'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928790, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wQ67Xo3j|v1_iDL/cSGF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 续航不如普通的手表', 'NewMsgId': 8927051855046493052, 'MsgSeq': 871413619}
2025-07-31 10:26:28 | INFO | 收到文本消息: 消息ID:648100674 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:续航不如普通的手表
2025-07-31 10:26:29 | DEBUG | 处理消息内容: '续航不如普通的手表'
2025-07-31 10:26:29 | DEBUG | 消息内容 '续航不如普通的手表' 不匹配任何命令，忽略
2025-07-31 10:26:31 | DEBUG | 收到消息: {'MsgId': 684680327, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n垃圾一笔'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928792, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_NIy35bn6|v1_wvgd3WrJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 垃圾一笔', 'NewMsgId': 3983853596861117305, 'MsgSeq': 871413620}
2025-07-31 10:26:31 | INFO | 收到文本消息: 消息ID:684680327 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:垃圾一笔
2025-07-31 10:26:32 | DEBUG | 处理消息内容: '垃圾一笔'
2025-07-31 10:26:32 | DEBUG | 消息内容 '垃圾一笔' 不匹配任何命令，忽略
2025-07-31 10:26:34 | DEBUG | 收到消息: {'MsgId': 1239981789, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n苹果起码轻便'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928793, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_iyC5PoUn|v1_bamiXU8T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 苹果起码轻便', 'NewMsgId': 5228220798606223540, 'MsgSeq': 871413621}
2025-07-31 10:26:34 | INFO | 收到文本消息: 消息ID:1239981789 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:苹果起码轻便
2025-07-31 10:26:34 | DEBUG | 处理消息内容: '苹果起码轻便'
2025-07-31 10:26:34 | DEBUG | 消息内容 '苹果起码轻便' 不匹配任何命令，忽略
2025-07-31 10:26:37 | DEBUG | 收到消息: {'MsgId': 926212083, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n那是你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928797, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_hwod1CLR|v1_rJbnjHRQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 那是你', 'NewMsgId': 4667697962462948929, 'MsgSeq': 871413622}
2025-07-31 10:26:37 | INFO | 收到文本消息: 消息ID:926212083 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:那是你
2025-07-31 10:26:37 | DEBUG | 处理消息内容: '那是你'
2025-07-31 10:26:37 | DEBUG | 消息内容 '那是你' 不匹配任何命令，忽略
2025-07-31 10:26:42 | DEBUG | 收到消息: {'MsgId': 607809693, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你喜欢轻便就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928804, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qcdXIX3g|v1_JoDHR3m1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你喜欢轻便就行了', 'NewMsgId': 5105226243692740289, 'MsgSeq': 871413623}
2025-07-31 10:26:42 | INFO | 收到文本消息: 消息ID:607809693 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你喜欢轻便就行了
2025-07-31 10:26:43 | DEBUG | 处理消息内容: '你喜欢轻便就行了'
2025-07-31 10:26:43 | DEBUG | 消息内容 '你喜欢轻便就行了' 不匹配任何命令，忽略
2025-07-31 10:26:49 | DEBUG | 收到消息: {'MsgId': 269040940, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我对这些没有要求'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928811, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_O2hFZdN7|v1_v2bM+yuK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我对这些没有要求', 'NewMsgId': 4535196299513716145, 'MsgSeq': 871413624}
2025-07-31 10:26:49 | INFO | 收到文本消息: 消息ID:269040940 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我对这些没有要求
2025-07-31 10:26:49 | DEBUG | 处理消息内容: '我对这些没有要求'
2025-07-31 10:26:49 | DEBUG | 消息内容 '我对这些没有要求' 不匹配任何命令，忽略
2025-07-31 10:26:52 | DEBUG | 收到消息: {'MsgId': 477109755, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n其实都没用'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928814, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_uQBfB4eR|v1_fPGKR1xj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 其实都没用', 'NewMsgId': 8821981320206663507, 'MsgSeq': 871413625}
2025-07-31 10:26:52 | INFO | 收到文本消息: 消息ID:477109755 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:其实都没用
2025-07-31 10:26:52 | DEBUG | 处理消息内容: '其实都没用'
2025-07-31 10:26:52 | DEBUG | 消息内容 '其实都没用' 不匹配任何命令，忽略
2025-07-31 10:26:57 | DEBUG | 收到消息: {'MsgId': 2132002709, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n买个蓝牙耳机'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928819, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1EqP1iWP|v1_g4OZF0TK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 买个蓝牙耳机', 'NewMsgId': 4026660244128120462, 'MsgSeq': 871413626}
2025-07-31 10:26:57 | INFO | 收到文本消息: 消息ID:2132002709 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:买个蓝牙耳机
2025-07-31 10:26:57 | DEBUG | 处理消息内容: '买个蓝牙耳机'
2025-07-31 10:26:57 | DEBUG | 消息内容 '买个蓝牙耳机' 不匹配任何命令，忽略
2025-07-31 10:27:07 | DEBUG | 收到消息: {'MsgId': 1964207763, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n比这强'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928828, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_aG/VOtyi|v1_yKlmwxUa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 比这强', 'NewMsgId': 8468888212799534335, 'MsgSeq': 871413627}
2025-07-31 10:27:07 | INFO | 收到文本消息: 消息ID:1964207763 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:比这强
2025-07-31 10:27:07 | DEBUG | 处理消息内容: '比这强'
2025-07-31 10:27:07 | DEBUG | 消息内容 '比这强' 不匹配任何命令，忽略
2025-07-31 10:27:34 | DEBUG | 收到消息: {'MsgId': 545251872, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你上个班还听蓝牙而已'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928856, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8fpKvv41|v1_EvyD4ALh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你上个班还听蓝牙而已', 'NewMsgId': 5720275209854928194, 'MsgSeq': 871413628}
2025-07-31 10:27:34 | INFO | 收到文本消息: 消息ID:545251872 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你上个班还听蓝牙而已
2025-07-31 10:27:35 | DEBUG | 处理消息内容: '你上个班还听蓝牙而已'
2025-07-31 10:27:35 | DEBUG | 消息内容 '你上个班还听蓝牙而已' 不匹配任何命令，忽略
2025-07-31 10:27:37 | DEBUG | 收到消息: {'MsgId': 672562653, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n蓝牙耳机'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928858, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2Dk8TBYf|v1_9PBLiOEq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 蓝牙耳机', 'NewMsgId': 3578590977779261260, 'MsgSeq': 871413629}
2025-07-31 10:27:37 | INFO | 收到文本消息: 消息ID:672562653 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:蓝牙耳机
2025-07-31 10:27:37 | DEBUG | 处理消息内容: '蓝牙耳机'
2025-07-31 10:27:37 | DEBUG | 消息内容 '蓝牙耳机' 不匹配任何命令，忽略
2025-07-31 10:27:40 | DEBUG | 收到消息: {'MsgId': 261037475, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n扯淡吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928859, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Irv2ZsP3|v1_5sg1bzqW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 扯淡吧', 'NewMsgId': 5766085447566238063, 'MsgSeq': 871413630}
2025-07-31 10:27:40 | INFO | 收到文本消息: 消息ID:261037475 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:扯淡吧
2025-07-31 10:27:40 | DEBUG | 处理消息内容: '扯淡吧'
2025-07-31 10:27:40 | DEBUG | 消息内容 '扯淡吧' 不匹配任何命令，忽略
2025-07-31 10:27:42 | DEBUG | 收到消息: {'MsgId': 402908087, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n上班手表震动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928863, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vIlRJ4qw|v1_9GCOL4mv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 上班手表震动', 'NewMsgId': 3301132023670759752, 'MsgSeq': 871413631}
2025-07-31 10:27:42 | INFO | 收到文本消息: 消息ID:402908087 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:上班手表震动
2025-07-31 10:27:43 | DEBUG | 处理消息内容: '上班手表震动'
2025-07-31 10:27:43 | DEBUG | 消息内容 '上班手表震动' 不匹配任何命令，忽略
2025-07-31 10:27:45 | DEBUG | 收到消息: {'MsgId': 525157944, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928864, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OJxUPnUk|v1_KAAuDCYj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 就行了', 'NewMsgId': 2616338961394948224, 'MsgSeq': 871413632}
2025-07-31 10:27:45 | INFO | 收到文本消息: 消息ID:525157944 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:就行了
2025-07-31 10:27:46 | DEBUG | 处理消息内容: '就行了'
2025-07-31 10:27:46 | DEBUG | 消息内容 '就行了' 不匹配任何命令，忽略
2025-07-31 10:28:08 | DEBUG | 收到消息: {'MsgId': 1979448039, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928890, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_gkaabO9H|v1_ERPhRfEr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女系列', 'NewMsgId': 3238671020162498772, 'MsgSeq': 871413633}
2025-07-31 10:28:08 | INFO | 收到文本消息: 消息ID:1979448039 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女系列
2025-07-31 10:28:08 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女系列 from wxid_ubbh6q832tcs21
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753928888.78秒前, 等待时间: 0.00秒
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-31 10:28:08 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女系列'
2025-07-31 10:28:08 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女系列
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7532989318484657699, Web ID: 7532989324985157172
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 1983
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 生成会话ID: local_1753928888778, 消息ID: a83fb641-3c0f-43e3-a047-8afb53ccc494
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女系列
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 请求头: {'Host': 'www.doubao.com', 'Connection': 'keep-alive', 'x-flow-trace': '04-ddbe216344d14a6b-12caae8813fe4054-01', 'sec-ch-ua-platform': '"Android"', 'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"', 'sec-ch-ua-mobile': '?1', 'Agw-Js-Conv': 'str, str', 'last-event-id': 'undefined', 'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36', 'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://www.doubao.com/chat/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'}
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\\u7cfb\\u5217\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": true, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "conversation_id": "0", "local_conversation_id": "local_1753928888778", "local_message_id": "a83fb641-3c0f-43e3-a047-8afb53ccc494"}
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7532989318484657699&device_plat...
2025-07-31 10:28:08 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 615 字符
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Thu, 31 Jul 2025 02:28:12 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '202507311028119E3A1EA03296AB6E7252', 'server-timing': 'inner; dur=564,tt_agw; dur=553', 'x-ms-token': 'z2D_Hq5VjHtDTPNMCYfQLs1qgWPxT1H6UtAIMO4DmQMdZOTHY8_tMZX_S-x9C2L72ypQactoy1pRTsMUADF274gHyxHMcmuqtKviMqw7', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f848a7143381b201e213f36881700a06ec99d981bc06fba187487c149f46077c6ff1f20852b5010e96d94f2031219dcacd3115b8dc476ef4d8ca715af3d9448fa011d141c5130ace1de10508b104694dd195fa0e5f02c0de10b47add52b1b7126c2', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-2507311028119E3A1EA03296AB6E7252-11560D72433874FA-00', 'x-tt-timestamp': '1753928892.136', 'via': 'cache11.cn7023[613,0]', 'timing-allow-origin': '*', 'eagleid': 'db999b1f17539288915506586e'}
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 35263 字节
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 35263 字节
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 33911 字符
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 323
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 事件类型: 2002
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 548
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 事件类型: 2010
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 检测到视频搜索意图
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 处理第 3 个事件，数据长度: 571
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 处理第 4 个事件，数据长度: 683
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 处理第 5 个事件，数据长度: 29724
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 收到视频搜索内容
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 找到 19 个视频卡片
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 1: #感受大自然的气息和美景 #分享时尚美女视频 #极品身材 #...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 2: #这谁顶得住啊 #🔥辣身材 #清纯甜美-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 3: 你的美丽真是令人惊叹，不仅有如诗如画的容貌，还有如此迷人的气...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 4: 军师这种怎么追-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 5: #抖音光合计划 
#街拍 #气质美女 完美身材-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 6: 《美女合集》来喽 关注哦 以后会更新美女视频-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 7: 美女变装-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 8: #泳池性感美女 #完美身材 #🔥辣身材 @蚊子包-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 9: #好身材研究员 梦中的那一位，自行解决-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 10: 我说我说女的你到处和别人说我是你的-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 11: 为什么你们都喜欢黑丝御姐大长腿呢-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 12: 麦麦甜不甜？-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 13: 每一位美女都有自己独特的气质和风格。她们的魅力或妩媚、或清纯...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 14: #原创视频 #陈七七 糟糕又心动了#这样的身材有人喜欢吗 #...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 15: 猜你喜欢-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 16: #俯卧撑挑战 #俯卧撑卡点挑战 #吻屏幕 #吻屏幕合集 2分...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 17: #美女舞蹈 #这才是甜妹该跳的舞 #这样跳能俘获你的芳心吗 ...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 18: #心动瞬间 #美女 #女生必看 #甜美女孩 上百位美女混剪辑...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 视频 19: 你心动了吗，-抖音...
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 累计收集到 19 个视频
2025-07-31 10:28:15 | INFO | [DoubaoVideoSearch] 完成状态：立即返回 19 个视频结果
2025-07-31 10:28:15 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 获取到 19 个视频结果
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: videos
2025-07-31 10:28:15 | INFO | [DoubaoVideoSearch] 收到19个视频结果
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 美女系列
2025-07-31 10:28:15 | INFO | [DoubaoVideoSearch] 选中视频: #感受大自然的气息和美景 #分享时尚美女视频 #极品身材 #凹凸有致-抖音...
2025-07-31 10:28:15 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7395268163627240739/?scene_from=douyin_h5_flow
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7395268163627240739/?scene_from=douyin_h5_flow
2025-07-31 10:28:15 | DEBUG | [DoubaoVideoSearch] 发送视频解析请求: http://api.xn--ei1aa.cn/API/douyin.php?url=https%3A//m.douyin.com/share/video/7395268163627240739/%3Fscene_from%3Ddouyin_h5_flow
2025-07-31 10:28:25 | DEBUG | [DoubaoVideoSearch] 视频解析API响应状态码: 502
2025-07-31 10:28:25 | WARNING | [DoubaoVideoSearch] 视频解析API返回错误状态码: 502
2025-07-31 10:28:25 | WARNING | [DoubaoVideoSearch] 视频解析失败，使用原始信息发送
2025-07-31 10:28:25 | DEBUG | [DoubaoVideoSearch] 开始发送视频分享消息到 55878994168@chatroom
2025-07-31 10:28:25 | DEBUG | [DoubaoVideoSearch] 视频信息 - 标题: #感受大自然的气息和美景 #分享时尚美女视频 #极品身材 #凹凸有致-抖音, 描述: 来源: 抖音
2025-07-31 10:28:25 | DEBUG | [DoubaoVideoSearch] 视频URL: https://m.douyin.com/share/video/7395268163627240739/?scene_from=douyin_h5_flow...
2025-07-31 10:28:25 | DEBUG | [DoubaoVideoSearch] XML消息长度: 1251 字符
2025-07-31 10:28:26 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>#感受大自然的气息和美景 #分享时尚美女视频 #极品身材 #凹凸有致-抖音</title><des>来源: 抖音</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://m.douyin.com/share/video/7395268163627240739/?scene_from=douyin_h5_flow</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-p-0015/oARUmAIeUBDBgsA1IgWABlEhnCFU9obEDmjNfp~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=idYOE3yRjQRRfqpcQYm3lyVStCI%3D&from=327834062&s=PackSourceEnum_SEARCH&se=false&sc=origin_cover&biz_tag=aweme_video&l=202507311028131720180001793432731</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-31 10:28:26 | INFO | [DoubaoVideoSearch] 视频分享消息发送成功，client_msg_id: 55878994168@chatroom_1753928905, new_msg_id: 6874493435607933001
2025-07-31 10:28:26 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 17.25秒
2025-07-31 10:28:26 | DEBUG | 处理消息内容: '找视频 美女系列'
2025-07-31 10:28:26 | DEBUG | 消息内容 '找视频 美女系列' 不匹配任何命令，忽略
2025-07-31 10:28:34 | DEBUG | 收到消息: {'MsgId': 855790473, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="ced96c7e70958d701baf402fa94f1ea8" encryver="1" cdnthumbaeskey="ced96c7e70958d701baf402fa94f1ea8" cdnthumburl="3057020100044b30490201000204f53349af02032e1d7b0204ad6196240204688ad4d3042430396163386537632d343231352d346165332d383334362d376461313939386163303234020405290a020201000405004c57c300" cdnthumblength="3455" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f53349af02032e1d7b0204ad6196240204688ad4d3042430396163386537632d343231352d346165332d383334362d376461313939386163303234020405290a020201000405004c57c300" length="82473" md5="ed4b89833f114d600b9c905a97421d8c" hevc_mid_size="82473">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwNTA5MDEwMDAwMDAwMTAiLCJwZHFoYXNoIjoiOTM1ODhiMGQ4YThmNGY0NGVmMDdlYTg3NGM0NzZhMDdlYTA2NzAwMmVjMTVmZjgyY2ZiOGRkNTE4YmJhOWY1MSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928915, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<mediaeditcontent />\n\t<sec_msg_node>\n\t\t<uuid>3bd699a84c56ae52ddcb2af6f7cc7fac_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CRG5+snn|v1_cQkcqDag</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一张图片', 'NewMsgId': 7166956092025782177, 'MsgSeq': 871413636}
2025-07-31 10:28:34 | INFO | 收到图片消息: 消息ID:855790473 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 XML:<?xml version="1.0"?><msg><img aeskey="ced96c7e70958d701baf402fa94f1ea8" encryver="1" cdnthumbaeskey="ced96c7e70958d701baf402fa94f1ea8" cdnthumburl="3057020100044b30490201000204f53349af02032e1d7b0204ad6196240204688ad4d3042430396163386537632d343231352d346165332d383334362d376461313939386163303234020405290a020201000405004c57c300" cdnthumblength="3455" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f53349af02032e1d7b0204ad6196240204688ad4d3042430396163386537632d343231352d346165332d383334362d376461313939386163303234020405290a020201000405004c57c300" length="82473" md5="ed4b89833f114d600b9c905a97421d8c" hevc_mid_size="82473"><secHashInfoBase64>eyJwaGFzaCI6IjUwNTA5MDEwMDAwMDAwMTAiLCJwZHFoYXNoIjoiOTM1ODhiMGQ4YThmNGY0NGVmMDdlYTg3NGM0NzZhMDdlYTA2NzAwMmVjMTVmZjgyY2ZiOGRkNTE4YmJhOWY1MSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 10:28:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 10:28:34 | INFO | [TimerTask] 缓存图片消息: 855790473
2025-07-31 10:28:39 | DEBUG | 收到消息: {'MsgId': 814866467, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n你们公司也这样吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928921, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_M94/8/wx|v1_ynflFNPM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 你们公司也这样吗', 'NewMsgId': 7273709219912724567, 'MsgSeq': 871413637}
2025-07-31 10:28:39 | INFO | 收到文本消息: 消息ID:814866467 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:你们公司也这样吗
2025-07-31 10:28:40 | DEBUG | 处理消息内容: '你们公司也这样吗'
2025-07-31 10:28:40 | DEBUG | 消息内容 '你们公司也这样吗' 不匹配任何命令，忽略
2025-07-31 10:28:42 | DEBUG | 收到消息: {'MsgId': 1264726848, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n拖拖拉拉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928924, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_znsc/Yic|v1_RahUS+rX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 拖拖拉拉', 'NewMsgId': 1110011191417981352, 'MsgSeq': 871413638}
2025-07-31 10:28:42 | INFO | 收到文本消息: 消息ID:1264726848 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:拖拖拉拉
2025-07-31 10:28:43 | DEBUG | 处理消息内容: '拖拖拉拉'
2025-07-31 10:28:43 | DEBUG | 消息内容 '拖拖拉拉' 不匹配任何命令，忽略
2025-07-31 10:28:59 | DEBUG | 收到消息: {'MsgId': 769620054, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n昨天放鸽子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928941, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_iDweFt5C|v1_FnXPcCc2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 昨天放鸽子', 'NewMsgId': 8815642047244337940, 'MsgSeq': 871413639}
2025-07-31 10:28:59 | INFO | 收到文本消息: 消息ID:769620054 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:昨天放鸽子
2025-07-31 10:29:00 | DEBUG | 处理消息内容: '昨天放鸽子'
2025-07-31 10:29:00 | DEBUG | 消息内容 '昨天放鸽子' 不匹配任何命令，忽略
2025-07-31 10:29:05 | DEBUG | 收到消息: {'MsgId': 1550238903, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n今天拖拉半小时'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928947, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nwDVvZhh|v1_bNXS1Zb0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 今天拖拉半小时', 'NewMsgId': 3465103986197853110, 'MsgSeq': 871413640}
2025-07-31 10:29:05 | INFO | 收到文本消息: 消息ID:1550238903 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:今天拖拉半小时
2025-07-31 10:29:05 | DEBUG | 处理消息内容: '今天拖拉半小时'
2025-07-31 10:29:05 | DEBUG | 消息内容 '今天拖拉半小时' 不匹配任何命令，忽略
2025-07-31 10:29:42 | DEBUG | 收到消息: {'MsgId': 1427211116, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n正常'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753928984, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_slTU9OZd|v1_Dz4GGK+G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 正常', 'NewMsgId': 7270449267954378727, 'MsgSeq': 871413641}
2025-07-31 10:29:42 | INFO | 收到文本消息: 消息ID:1427211116 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:正常
2025-07-31 10:29:43 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:3f9eb9cb80c62002c8fd8716c3c2e8cd 总长度:9992069
2025-07-31 10:29:43 | DEBUG | 处理消息内容: '正常'
2025-07-31 10:29:43 | DEBUG | 消息内容 '正常' 不匹配任何命令，忽略
2025-07-31 10:30:09 | DEBUG | 收到消息: {'MsgId': 1163189492, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n九点开会十点到十一点再做报告[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753929011, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_R6sGuCYA|v1_mz30JkBM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 九点开会十点到十一点再做报告[抠鼻]', 'NewMsgId': 5778909366275824000, 'MsgSeq': 871413644}
2025-07-31 10:30:09 | INFO | 收到文本消息: 消息ID:1163189492 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:九点开会十点到十一点再做报告[抠鼻]
2025-07-31 10:30:10 | DEBUG | 处理消息内容: '九点开会十点到十一点再做报告[抠鼻]'
2025-07-31 10:30:10 | DEBUG | 消息内容 '九点开会十点到十一点再做报告[抠鼻]' 不匹配任何命令，忽略
2025-07-31 10:30:45 | DEBUG | 收到消息: {'MsgId': 2021203619, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n这种是好公司的表现'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753929047, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PfMpX7P1|v1_L7Zt39Jx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 这种是好公司的表现', 'NewMsgId': 1618893138981995567, 'MsgSeq': 871413645}
2025-07-31 10:30:45 | INFO | 收到文本消息: 消息ID:2021203619 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:这种是好公司的表现
2025-07-31 10:30:46 | DEBUG | 处理消息内容: '这种是好公司的表现'
2025-07-31 10:30:46 | DEBUG | 消息内容 '这种是好公司的表现' 不匹配任何命令，忽略
2025-07-31 10:31:33 | DEBUG | 收到消息: {'MsgId': 2047864022, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="91b0b203259d0c9cb86eca35beba4d73" len = "2269646" productid="" androidmd5="91b0b203259d0c9cb86eca35beba4d73" androidlen="2269646" s60v3md5 = "91b0b203259d0c9cb86eca35beba4d73" s60v3len="2269646" s60v5md5 = "91b0b203259d0c9cb86eca35beba4d73" s60v5len="2269646" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=91b0b203259d0c9cb86eca35beba4d73&amp;filekey=30440201010430302e02016e0402535a04203931623062323033323539643063396362383665636133356265626134643733020322a1ce040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268725331000e34ac1fe6aeb70000006e01004fb1535a21b4a8809058bde62&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=956693299d62d3261ce81742848cf5c1&amp;filekey=30440201010430302e02016e0402535a04203935363639333239396436326433323631636538313734323834386366356331020322a1d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2687253320001dd091fe6aeb70000006e02004fb2535a21b4a8809058bde92&amp;ef=2&amp;bizid=1022" aeskey= "045dc30d39804be1b7ccc6a1edc04bd5" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0efdb3f78a49eed4eff18f5c60853999&amp;filekey=30440201010430302e02016e0402535a042030656664623366373861343965656434656666313866356336303835333939390203016a10040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26872533200047b3c1fe6aeb70000006e03004fb3535a21b4a8809058bdec0&amp;ef=3&amp;bizid=1022" externmd5 = "cbccc6e867e4d2138aecacfed1cac7d7" width= "320" height= "320" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753929095, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_a2XO2UgI|v1_bzfvMtSG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 6306797534437837731, 'MsgSeq': 871413646}
2025-07-31 10:31:33 | INFO | 收到表情消息: 消息ID:2047864022 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:91b0b203259d0c9cb86eca35beba4d73 大小:2269646
2025-07-31 10:31:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6306797534437837731
2025-07-31 10:32:18 | DEBUG | 收到消息: {'MsgId': 739861465, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3f9eb9cb80c62002c8fd8716c3c2e8cd" len="350053" productid="" androidmd5="3f9eb9cb80c62002c8fd8716c3c2e8cd" androidlen="350053" s60v3md5="3f9eb9cb80c62002c8fd8716c3c2e8cd" s60v3len="350053" s60v5md5="3f9eb9cb80c62002c8fd8716c3c2e8cd" s60v5len="350053" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=3f9eb9cb80c62002c8fd8716c3c2e8cd&amp;filekey=30440201010430302e02016e0402535a042033663965623963623830633632303032633866643837313663336332653863640203055765040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267f3d55a00037b504b2987450000006e01004fb1535a05e6bae1e00dbaf6a&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a5e6921065e91dda67d903cd886bb0c8&amp;filekey=30440201010430302e02016e0402535a042061356536393231303635653931646461363764393033636438383662623063380203055770040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267f3d55a0004aee84b2987450000006e02004fb2535a05e6bae1e00dbaf84&amp;ef=2&amp;bizid=1022" aeskey="d1f73d40560f4c29b5d3312763f9f338" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=de24e24c6e96e13d9cd76c4d8e203f1b&amp;filekey=30440201010430302e02016e0402535a04206465323465323463366539366531336439636437366334643865323033663162020300fc90040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267f3d55a000571464b2987450000006e03004fb3535a05e6bae1e00dbaf93&amp;ef=3&amp;bizid=1022" externmd5="402ac703c663116c4ed71a4906aa6098" width="398" height="397" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753929140, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0C8XFQEP|v1_ElmOMXQB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 2926218790049617175, 'MsgSeq': 871413647}
2025-07-31 10:32:18 | INFO | 收到表情消息: 消息ID:739861465 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:3f9eb9cb80c62002c8fd8716c3c2e8cd 大小:350053
2025-07-31 10:32:18 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2926218790049617175
2025-07-31 10:44:10 | DEBUG | 收到消息: {'MsgId': 1572464026, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753929852, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_bby1FNar|v1_s/OPDgoH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女系列', 'NewMsgId': 199513106599495066, 'MsgSeq': 871413648}
2025-07-31 10:44:10 | INFO | 收到文本消息: 消息ID:1572464026 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女系列
2025-07-31 10:44:10 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女系列 from wxid_ubbh6q832tcs21
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 962.11秒前, 等待时间: 0.00秒
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-31 10:44:10 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女系列'
2025-07-31 10:44:10 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女系列
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7532989318484657699, Web ID: 7532989324985157172
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 1983
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 生成会话ID: local_1753929850889, 消息ID: 0ef79dd5-cf5f-49ac-b8af-8c1dd60a3158
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女系列
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 请求头: {'Host': 'www.doubao.com', 'Connection': 'keep-alive', 'x-flow-trace': '04-b2857b632e2b4875-7a429a648a6d4a50-01', 'sec-ch-ua-platform': '"Android"', 'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"', 'sec-ch-ua-mobile': '?1', 'Agw-Js-Conv': 'str, str', 'last-event-id': 'undefined', 'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36', 'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://www.doubao.com/chat/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'}
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\\u7cfb\\u5217\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": true, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "conversation_id": "0", "local_conversation_id": "local_1753929850889", "local_message_id": "0ef79dd5-cf5f-49ac-b8af-8c1dd60a3158"}
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7532989318484657699&device_plat...
2025-07-31 10:44:10 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 615 字符
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Thu, 31 Jul 2025 02:44:14 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '20250731104413BCF52322D2AD452502BF', 'server-timing': 'inner; dur=511,tt_agw; dur=500', 'x-ms-token': 'mpgw3IzC2G2G9op3dI9N9yhLo_EOzSyUp6A_VE94-axI6Knp7vYu5GjR-e5_Wek2cnjJkJK7L-43b-YQzy4YzU49ib27cFdwqEMXONbB', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48aab907d6158e5ef1736b5181d2dded510eb81ca47b7ef6d6f501209a168bcecf082a66062eb3a850a06b7520e1082bf377b337dabfcd9a69be6a3c3be3b29dd9bc8b13791483843b9a4c0eb0db0d6c311429ac78b3151449b654cb58d36f2600cca6a20f079f128819734c60c6a9bcac5', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-250731104413BCF52322D2AD452502BF-110FAE4D358D7A4B-00', 'x-tt-timestamp': '1753929854.221', 'via': 'ens-vcache5.cn8240[551,0]', 'timing-allow-origin': '*', 'eagleid': '7754819917539298536942067e'}
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 35251 字节
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 35251 字节
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 33912 字符
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 323
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 事件类型: 2002
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 548
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 事件类型: 2010
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 检测到视频搜索意图
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 处理第 3 个事件，数据长度: 571
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 处理第 4 个事件，数据长度: 683
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 处理第 5 个事件，数据长度: 29731
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 收到视频搜索内容
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 找到 19 个视频卡片
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 1: #感受大自然的气息和美景 #分享时尚美女视频 #极品身材 #...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 2: 你的美丽真是令人惊叹，不仅有如诗如画的容貌，还有如此迷人的气...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 3: #这谁顶得住啊 #🔥辣身材 #清纯甜美-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 4: #抖音光合计划 
#街拍 #气质美女 完美身材-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 5: 军师这种怎么追-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 6: #好身材研究员 梦中的那一位，自行解决-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 7: #原创视频 #陈七七 糟糕又心动了#这样的身材有人喜欢吗 #...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 8: 每一位美女都有自己独特的气质和风格。她们的魅力或妩媚、或清纯...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 9: #分享时尚美女视频 #恒大歌舞团美女排名  #丰满莹润的中式...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 10: 为什么你们都喜欢黑丝御姐大长腿呢-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 11: #泳池性感美女 #完美身材 #🔥辣身材 @蚊子包-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 12: 我说我说女的你到处和别人说我是你的-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 13: 当我望向你的时候 我们之间隔着历史的长河-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 14: 猜你喜欢-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 15: #俯卧撑挑战 #俯卧撑卡点挑战 #吻屏幕 #吻屏幕合集 2分...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 16: #这谁顶得住啊 #完美身材 #🔥辣身材 #身材天花板-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 17: #心动瞬间 #美女 #女生必看 #甜美女孩 上百位美女混剪辑...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 18: 《纯  欲  大  合  集》-抖音...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 视频 19: #好看的小姐姐 #美女 #这谁顶得住啊 #完美身材 #甜系女...
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 累计收集到 19 个视频
2025-07-31 10:44:17 | INFO | [DoubaoVideoSearch] 完成状态：立即返回 19 个视频结果
2025-07-31 10:44:17 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 获取到 19 个视频结果
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: videos
2025-07-31 10:44:17 | INFO | [DoubaoVideoSearch] 收到19个视频结果
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 美女系列
2025-07-31 10:44:17 | INFO | [DoubaoVideoSearch] 选中视频: 你的美丽真是令人惊叹，不仅有如诗如画的容貌，还有如此迷人的气质，真是个无法抗拒的“诗意少女”。-抖音...
2025-07-31 10:44:17 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7417305603636202752/?scene_from=douyin_h5_flow
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7417305603636202752/?scene_from=douyin_h5_flow
2025-07-31 10:44:17 | DEBUG | [DoubaoVideoSearch] 发送视频解析请求: http://api.xn--ei1aa.cn/API/douyin.php?url=https%3A//m.douyin.com/share/video/7417305603636202752/%3Fscene_from%3Ddouyin_h5_flow
2025-07-31 10:44:27 | DEBUG | [DoubaoVideoSearch] 视频解析API响应状态码: 502
2025-07-31 10:44:27 | WARNING | [DoubaoVideoSearch] 视频解析API返回错误状态码: 502
2025-07-31 10:44:27 | WARNING | [DoubaoVideoSearch] 视频解析失败，使用原始信息发送
2025-07-31 10:44:27 | DEBUG | [DoubaoVideoSearch] 开始发送视频分享消息到 55878994168@chatroom
2025-07-31 10:44:27 | DEBUG | [DoubaoVideoSearch] 视频信息 - 标题: 你的美丽真是令人惊叹，不仅有如诗如画的容貌，还有如此迷人的气质，真是个无法抗拒的“诗意少女”。-抖音, 描述: 来源: 抖音
2025-07-31 10:44:27 | DEBUG | [DoubaoVideoSearch] 视频URL: https://m.douyin.com/share/video/7417305603636202752/?scene_from=douyin_h5_flow...
2025-07-31 10:44:27 | DEBUG | [DoubaoVideoSearch] XML消息长度: 1264 字符
2025-07-31 10:44:28 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>你的美丽真是令人惊叹，不仅有如诗如画的容貌，还有如此迷人的气质，真是个无法抗拒的“诗意少女”。-抖音</title><des>来源: 抖音</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://m.douyin.com/share/video/7417305603636202752/?scene_from=douyin_h5_flow</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-p-0015/oU3v8hPiDve3AInI5BizAkHBg0DxqArDvedADz~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=i50GpdOiB8ZNORXWxQKpJRdDkF8%3D&from=327834062&s=PackSourceEnum_SEARCH&se=false&sc=origin_cover&biz_tag=aweme_video&l=20250731104415000000000000947B229</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-31 10:44:28 | INFO | [DoubaoVideoSearch] 视频分享消息发送成功，client_msg_id: 55878994168@chatroom_1753929868, new_msg_id: 8913958536301603658
2025-07-31 10:44:28 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 17.40秒
2025-07-31 10:44:28 | DEBUG | 处理消息内容: '找视频 美女系列'
2025-07-31 10:44:28 | DEBUG | 消息内容 '找视频 美女系列' 不匹配任何命令，忽略
2025-07-31 10:45:54 | INFO | [TempFileManager] 开始清理临时文件...
