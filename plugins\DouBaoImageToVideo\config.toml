[DouBaoImageToVideo]
enable = true
command = ["豆包图生视频", "豆包视频测试"]
command-format = """
🎬 豆包AI图生视频功能

📝 图生视频使用方法：
• 引用图片 + 豆包视频 [提示词] - 处理引用的图片生成视频
• 豆包图生视频 [提示词] [图片路径] - 指定图片路径生成视频
• 豆包视频测试 [提示词] - 使用默认测试图片
• 豆包视频测试 - 使用默认提示词和默认测试图片

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg

⚙️ 处理步骤：
1. 上传图片到豆包AI
2. 提交视频生成任务
3. 等待视频生成完成
4. 下载并发送生成的视频

每一步都会显示详细状态信息，方便调试。
"""

[DouBaoImageToVideo.quote]
command = ["豆包视频"]
command-format = "引用图片并发送: 豆包视频 [提示词]"

[DouBaoImageToVideo.api]
# 豆包AI相关配置
base_url = "https://www.doubao.com"  # 豆包AI网站地址
api_key = "i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf"  # 豆包AI的Cookie字符串
model = "doubao-video-generation"  # 豆包AI的图生视频模型名称

[DouBaoImageToVideo.rate_limit]
cooldown = 30  # 视频生成需要更长的冷却时间

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
